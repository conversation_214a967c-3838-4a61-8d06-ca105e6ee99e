<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.ResumeProjectContentMapper">

    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.ResumeProjectContent">
            <id property="conId" column="con_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="text" column="text" jdbcType="VARCHAR"/>
            <result property="contentOrder" column="content_order" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        con_id,project_id,text,
        content_order,create_time,update_time
    </sql>
</mapper>
