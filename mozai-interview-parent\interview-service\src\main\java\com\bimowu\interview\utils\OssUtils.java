package com.bimowu.interview.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.UUID;

/**
 * 阿里云OSS工具类
 */
@Component
@Slf4j
public class OssUtils {

    @Value("${aliyun.oss.endpoint:}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId:}")
    private String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret:}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucketName:}")
    private String bucketName;

    /**
     * 上传文件到OSS
     *
     * @param file       文件对象
     * @param objectName 对象名称
     * @return 访问URL
     */
    public String uploadFile(File file, String objectName) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // 创建PutObjectRequest对象
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, file);
            // 上传文件
            ossClient.putObject(putObjectRequest);
            // 设置URL过期时间为1小时
            Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000);
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容
            URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
            return url.toString();
        } catch (Exception e) {
            log.error("上传文件到OSS失败", e);
            throw new RuntimeException("上传文件到OSS失败: " + e.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 上传文件到OSS
     *
     * @param inputStream 输入流
     * @param objectName  对象名称
     * @return 访问URL
     */
    public String uploadFile(InputStream inputStream, String objectName) {
        log.info("通过流上传文件到OSS，对象名: {}, 配置: endpoint={}, bucketName={}", objectName, endpoint, bucketName);
        
        // 检查OSS配置
        if (endpoint == null || endpoint.isEmpty() || 
            accessKeyId == null || accessKeyId.isEmpty() || 
            accessKeySecret == null || accessKeySecret.isEmpty() || 
            bucketName == null || bucketName.isEmpty()) {
            
            log.error("OSS配置不完整，endpoint={}, accessKeyId={}, accessKeySecret={}, bucketName={}", 
                    endpoint, accessKeyId, 
                    (accessKeySecret != null ? accessKeySecret.substring(0, Math.min(5, accessKeySecret.length())) + "..." : "null"), 
                    bucketName);
            throw new RuntimeException("OSS配置不完整，请检查配置信息");
        }
        
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // 上传文件
            log.info("开始上传文件到OSS，对象名: {}", objectName);
            ossClient.putObject(bucketName, objectName, inputStream);
            String url = "https://" + bucketName + "." + endpoint + "/" + objectName;
            log.info("文件上传到OSS成功，URL: {}", url);
            return url;
        } catch (Exception e) {
            log.error("上传文件到OSS失败, 对象名: {}, 异常: {}", objectName, e.getMessage(), e);
            throw new RuntimeException("上传文件到OSS失败: " + e.getMessage(), e);
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 上传MultipartFile到OSS
     *
     * @param file 上传的文件
     * @return 文件访问URL
     * @throws IOException IO异常
     */
    public String uploadFile(MultipartFile file) throws IOException {
        log.info("开始上传文件到OSS，文件名: {}, 大小: {}", file.getOriginalFilename(), file.getSize());
        
        // 获取文件流前先将文件内容复制到字节数组，避免重复读取临时文件
        byte[] fileContent = file.getBytes();
        log.info("已读取文件内容到内存，大小: {}", fileContent.length);
        
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String objectName = "interview/audio/" + UUID.randomUUID().toString() + extension;
        
        // 使用ByteArrayInputStream代替直接从MultipartFile获取流
        try (java.io.ByteArrayInputStream inputStream = new java.io.ByteArrayInputStream(fileContent)) {
            String url = uploadFile(inputStream, objectName);
            log.info("文件上传到OSS成功，对象名: {}", objectName);
            return url;
        } catch (Exception e) {
            log.error("上传文件到OSS失败", e);
            throw e;
        }
    }
    
    /**
     * 上传视频文件到OSS
     *
     * @param videoFile 视频文件
     * @return 文件访问URL
     * @throws IOException IO异常
     */
    public String uploadVideo(MultipartFile videoFile) throws IOException {
        log.info("开始上传视频文件到OSS，文件名: {}, 大小: {}, 配置信息: endpoint={}, bucketName={}", 
                videoFile.getOriginalFilename(), videoFile.getSize(), endpoint, bucketName);
        
        // 视频文件可能较大，使用流式传输而不是一次性加载到内存
        String originalFilename = videoFile.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        
        // 将视频文件存储到OSS的interview/video目录下
        String objectName = "interview/video/" + UUID.randomUUID().toString() + extension;
        
        try (InputStream inputStream = videoFile.getInputStream()) {
            log.info("开始上传视频文件到OSS，对象名: {}", objectName);
            String url = uploadFile(inputStream, objectName);
            log.info("视频文件上传到OSS成功，对象名: {}, URL: {}", objectName, url);
            return url;
        } catch (Exception e) {
            log.error("上传视频文件到OSS失败，对象名: {}, 异常: {}", objectName, e.getMessage(), e);
            throw new RuntimeException("上传视频文件到OSS失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            return ".tmp"; // 默认扩展名
        }
        return filename.substring(filename.lastIndexOf("."));
    }
} 