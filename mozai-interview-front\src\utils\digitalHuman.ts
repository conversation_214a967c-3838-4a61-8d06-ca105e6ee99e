import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTF } from 'three/examples/jsm/loaders/GLTFLoader.js'

const READY_PLAYER_ME_BASE_URL = 'https://models.readyplayer.me/';
const DEFAULT_AVATARS = [
  'https://cdn.jsdelivr.net/gh/microsoft/ai-edu@67db9213/B-教学案例与实践/B6-人工智能基础实践案例/examples/data/talking-head-anime-2-demo/assets/vroid/model.gltf', // 女性模型
  'https://cdn.jsdelivr.net/gh/KhronosGroup/glTF-Sample-Models@main/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb'  // 男性模型
];

// 备用静态模型（如果上面的动态模型不可用）
const FALLBACK_AVATARS = [
  'https://cdn.jsdelivr.net/gh/KhronosGroup/glTF-Sample-Models@main/2.0/BrainStem/glTF/BrainStem.gltf', // 通用模型1
  'https://cdn.jsdelivr.net/gh/KhronosGroup/glTF-Sample-Models@main/2.0/Duck/glTF/Duck.gltf' // 通用模型2
];

// 本地模型（最可靠选项，如果没有3D模型，可以使用静态图片作为回退选项）
const LOCAL_AVATARS = [
  {
    url: '/models/digital-human-female.glb', // 本地女性模型 - 使用我们自己生成的模型
    imageUrl: '/imgs/digital-human.png'      // 静态图片备用
  },
  {
    url: '/models/digital-human-male.glb',   // 本地男性模型 - 使用我们自己生成的模型
    imageUrl: '/imgs/digital-human.png'      // 静态图片备用
  }
];

// 数字人状态
export enum DigitalHumanState {
  IDLE = 'idle',
  TALKING = 'talking',
  LISTENING = 'listening',
  THINKING = 'thinking'
}

export interface DigitalHumanOptions {
  container: HTMLElement;
  onLoad?: () => void;
  onError?: (error: any) => void;
  gender?: 'male' | 'female';
  customModelUrl?: string;
}

export class DigitalHuman {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private controls: OrbitControls;
  private avatar: THREE.Object3D | null = null;
  private mixer: THREE.AnimationMixer | null = null;
  private animations: Map<string, THREE.AnimationAction> = new Map();
  private currentState: DigitalHumanState = DigitalHumanState.IDLE;
  private isLoaded: boolean = false;
  private container: HTMLElement;
  private frameId: number = 0;
  private clock: THREE.Clock;

  constructor(options: DigitalHumanOptions) {
    this.container = options.container;
    this.clock = new THREE.Clock();

    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x000000);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
        45,
        this.container.clientWidth / this.container.clientHeight,
        0.1,
        1000
    );
    this.camera.position.set(0, 1.6, 2);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    this.container.appendChild(this.renderer.domElement);

    // 添加控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 1.5, 0);
    this.controls.update();

    // 添加灯光
    this.addLights();

    // 加载模型
    this.loadModel(options);

    // 调整大小事件监听
    window.addEventListener('resize', this.onWindowResize.bind(this));

    // 开始动画循环
    this.animate();
  }

  private addLights(): void {
    // 添加环境光
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    this.scene.add(ambientLight);

    // 添加方向光
    const dirLight = new THREE.DirectionalLight(0xffffff, 1);
    dirLight.position.set(1, 2, 3);
    this.scene.add(dirLight);

    // 前置光源
    const frontLight = new THREE.DirectionalLight(0xffffff, 0.8);
    frontLight.position.set(0, 1, 2);
    this.scene.add(frontLight);
  }

  private loadModel(options: DigitalHumanOptions): void {
    const loader = new GLTFLoader();

    // 尝试使用自定义URL（如果有）
    if (options.customModelUrl) {
      this.loadModelWithURL(loader, options.customModelUrl, options);
      return;
    }

    // 由于本地模型加载问题，直接使用内存中生成的模型
    console.log('直接使用内存生成的数字人模型');
    this.createInMemoryModel(options);

    // 注释掉尝试加载外部模型的代码，避免404错误
    // this.tryLoadModelWithFallbacks(loader, options);
  }

  /**
   * 尝试使用多级备用方案加载模型
   */
  private tryLoadModelWithFallbacks(loader: GLTFLoader, options: DigitalHumanOptions): void {
    const localModel = options.gender === 'male' ? LOCAL_AVATARS[1] : LOCAL_AVATARS[0];
    const defaultModel = options.gender === 'male' ? DEFAULT_AVATARS[1] : DEFAULT_AVATARS[0];
    const fallbackModel = options.gender === 'male' ? FALLBACK_AVATARS[1] : FALLBACK_AVATARS[0];

    console.log('加载本地数字人模型:', localModel.url);

    // 1. 尝试加载本地模型
    this.loadModelWithURL(
        loader,
        localModel.url,
        options,
        // 如果本地模型失败，尝试默认远程模型
        () => {
          console.log('本地模型加载失败，尝试加载远程模型:', defaultModel);
          this.loadModelWithURL(
              loader,
              defaultModel,
              options,
              // 如果默认远程模型失败，尝试备用模型
              () => {
                console.log('远程模型加载失败，尝试加载备用模型:', fallbackModel);
                this.loadModelWithURL(
                    loader,
                    fallbackModel,
                    options,
                    // 如果备用模型也失败，尝试内存中创建模型
                    () => {
                      console.log('所有远程模型加载失败，尝试创建内存模型');
                      this.createInMemoryModel(options);
                    }
                );
              }
          );
        }
    );
  }

  /**
   * 使用指定的URL加载模型
   */
  private loadModelWithURL(
      loader: GLTFLoader,
      url: string,
      options: DigitalHumanOptions,
      onError?: () => void
  ): void {
    // 为了避免CORS和缓存问题，添加时间戳参数
    const modelUrl = url.includes('http') ?
        `${url}${url.includes('?') ? '&' : '?'}t=${Date.now()}` :
        url;

    // 如果是远程URL，先检查资源是否存在并且格式正确
    if (url.includes('http')) {
      this.checkResourceExists(url)
          .then(isValid => {
            if (isValid) {
              this.actuallyLoadModel(loader, modelUrl, options, onError);
            } else {
              console.error(`URL资源无效或不可访问: ${url}`);
              if (onError) onError();
              else this.createDefaultAvatar(options);
            }
          })
          .catch(() => {
            console.error(`检查URL资源时出错: ${url}`);
            if (onError) onError();
            else this.createDefaultAvatar(options);
          });
    } else {
      // 本地资源直接加载
      this.actuallyLoadModel(loader, modelUrl, options, onError);
    }
  }

  /**
   * 检查远程资源是否存在并且是有效的GLB/GLTF文件
   */
  private async checkResourceExists(url: string): Promise<boolean> {
    try {
      // 先尝试HEAD请求检查资源存在
      const response = await fetch(url, { method: 'HEAD' });

      if (!response.ok) return false;

      // 检查内容类型是否合法
      const contentType = response.headers.get('content-type');

      // GLB/GLTF文件的典型MIME类型
      const validTypes = [
        'model/gltf+json',
        'model/gltf-binary',
        'application/octet-stream',
        'application/json'
      ];

      // 如果没有内容类型或是合法类型，认为资源是有效的
      return !contentType || validTypes.some(type => contentType.includes(type));
    } catch (error) {
      console.error('检查资源时出错:', error);
      return false;
    }
  }

  /**
   * 实际加载模型的方法
   */
  private actuallyLoadModel(
      loader: GLTFLoader,
      url: string,
      options: DigitalHumanOptions,
      onError?: () => void
  ): void {
    loader.load(
        url,
        (gltf: GLTF) => {
          // 加载成功
          this.avatar = gltf.scene;
          this.scene.add(this.avatar);

          // 调整模型位置和大小
          this.avatar.position.set(0, 0, 0);
          this.avatar.scale.set(1, 1, 1);

          // 设置动画
          if (gltf.animations && gltf.animations.length > 0) {
            this.mixer = new THREE.AnimationMixer(this.avatar);

            gltf.animations.forEach((clip: THREE.AnimationClip) => {
              if (this.mixer) {  // 添加null检查
                const action = this.mixer.clipAction(clip);
                this.animations.set(clip.name, action);

                // 根据动画名称识别状态
                if (clip.name.includes('Idle')) {
                  this.animations.set(DigitalHumanState.IDLE, action);
                } else if (clip.name.includes('Talk') || clip.name.includes('Speaking')) {
                  this.animations.set(DigitalHumanState.TALKING, action);
                } else if (clip.name.includes('Listen')) {
                  this.animations.set(DigitalHumanState.LISTENING, action);
                }
              }
            });

            // 如果没有找到对应的动画，使用默认的
            if (!this.animations.has(DigitalHumanState.IDLE) && this.animations.size > 0) {
              const defaultAction = this.animations.values().next().value;
              if (defaultAction) {
                this.animations.set(DigitalHumanState.IDLE, defaultAction);
              }
            }

            // 开始播放默认动画
            this.playAnimation(DigitalHumanState.IDLE);
          } else {
            console.log('模型没有动画，将使用静态展示');
          }

          // 调整相机和控制器
          this.camera.lookAt(new THREE.Vector3(0, 1.5, 0));
          this.controls.target.set(0, 1.5, 0);
          this.controls.update();

          this.isLoaded = true;
          if (options.onLoad) options.onLoad();
        },
        (xhr: ProgressEvent) => {
          // 加载进度
          const percentage = xhr.loaded && xhr.total ? (xhr.loaded / xhr.total) * 100 : 0;
          console.log(`数字人模型加载进度: ${Math.round(percentage)}%`);
        },
        (error: any) => {
          // 加载错误
          console.error(`加载模型失败(${url}):`, error);

          if (onError) {
            // 如果提供了错误回调，调用它来尝试下一个备用选项
            onError();
          } else {
            // 如果没有更多备用选项，使用静态图片
            this.createDefaultAvatar(options);
          }
        }
    );
  }

  /**
   * 播放指定状态的动画
   */
  public playAnimation(state: DigitalHumanState): void {
    // 如果没有动画列表，直接返回
    if (!this.animations.size) return;

    // 获取当前动作和新动作
    const currentAction = this.animations.get(this.currentState);
    const newAction = this.animations.get(state);

    // 只有状态变化才执行动画切换
    if (this.currentState !== state && currentAction && newAction) {
      // 停止当前动作
      if (typeof currentAction.stop === 'function') {
        currentAction.stop();
      } else if (typeof currentAction.fadeOut === 'function') {
        currentAction.fadeOut(0.5);
      }

      // 播放新动作
      if (typeof newAction.reset === 'function' &&
          typeof newAction.fadeIn === 'function' &&
          typeof newAction.play === 'function') {
        newAction.reset().fadeIn(0.5).play();
      } else if (typeof newAction.play === 'function') {
        newAction.play();
      }

      // 更新当前状态
      this.currentState = state;
    } else if (!currentAction && newAction) {
      // 如果当前没有动作但有新动作
      if (typeof newAction.reset === 'function' &&
          typeof newAction.fadeIn === 'function' &&
          typeof newAction.play === 'function') {
        newAction.reset().fadeIn(0.5).play();
      } else if (typeof newAction.play === 'function') {
        newAction.play();
      }

      // 更新当前状态
      this.currentState = state;
    }
  }

  /**
   * 开始说话动画
   */
  public startTalking(): void {
    this.playAnimation(DigitalHumanState.TALKING);
  }

  /**
   * 停止说话，回到空闲状态
   */
  public stopTalking(): void {
    this.playAnimation(DigitalHumanState.IDLE);
  }

  /**
   * 设置数字人位置
   */
  public setPosition(x: number, y: number, z: number): void {
    if (this.avatar) {
      this.avatar.position.set(x, y, z);
    }
  }

  /**
   * 调整数字人视角
   */
  public lookAt(x: number, y: number, z: number): void {
    if (this.avatar) {
      this.avatar.lookAt(new THREE.Vector3(x, y, z));
    }
  }

  /**
   * 窗口大小变化处理
   */
  private onWindowResize(): void {
    if (!this.container) return;

    this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
  }

  /**
   * 动画循环
   */
  private animate(): void {
    this.frameId = requestAnimationFrame(this.animate.bind(this));

    // 增加动画时间
    this.animationTime += 0.05;

    // 更新动画混合器
    if (this.mixer) {
      const delta = this.clock.getDelta();
      this.mixer.update(delta);
    }

    // 处理自定义动画
    this.updateCustomAnimations();

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }

  /**
   * 更新自定义动画
   */
  private updateCustomAnimations(): void {
    // 处理说话动画
    if (this.talkingMouth) {
      // 使用sin函数创建嘴巴开合效果
      const scale = 1 + Math.abs(Math.sin(this.animationTime * 10)) * 2;
      this.talkingMouth.scale.y = scale;
    }

    // 处理听动画
    if (this.listeningMouth) {
      // 使用sin函数创建点头效果
      const originalZ = 0.35; // 原始Z坐标
      const nodAmount = Math.sin(this.animationTime * 3) * 0.03;
      this.listeningMouth.position.z = originalZ + nodAmount;
    }
  }

  /**
   * 销毁数字人实例，释放资源
   */
  public dispose(): void {
    if (this.frameId) {
      cancelAnimationFrame(this.frameId);
    }

    // 移除事件监听
    window.removeEventListener('resize', this.onWindowResize.bind(this));

    // 移除渲染器DOM
    if (this.container && this.renderer.domElement) {
      this.container.removeChild(this.renderer.domElement);
    }

    // 释放资源
    this.scene.clear();
    this.renderer.dispose();
  }

  /**
   * 创建默认几何体作为替代模型
   */
  private createDefaultAvatar(options?: DigitalHumanOptions): void {
    try {
      // 尝试使用静态图片创建平面
      const textureLoader = new THREE.TextureLoader();
      const staticImageUrl = '/imgs/digital-human.png';

      console.log('尝试加载静态图片:', staticImageUrl);

      textureLoader.load(
          staticImageUrl,
          (texture) => {
            try {
              // 创建一个平面几何体
              const ratio = texture.image.height / texture.image.width;
              const width = 2;
              const height = width * ratio;

              const geometry = new THREE.PlaneGeometry(width, height);
              const material = new THREE.MeshBasicMaterial({
                map: texture,
                transparent: true,
                side: THREE.DoubleSide
              });

              const plane = new THREE.Mesh(geometry, material);
              plane.position.set(0, 1.5, 0);

              // 添加到场景
              this.avatar = plane;
              this.scene.add(this.avatar);

              // 标记为已加载
              this.isLoaded = true;

              console.log('已创建静态图片模型作为数字人');

              if (options?.onLoad) options.onLoad();
            } catch (error) {
              console.error('创建平面几何体失败:', error);
              this.createDefaultCube(options);
            }
          },
          undefined,
          (error) => {
            // 静态图片加载失败，创建一个简单的立方体
            console.error('静态图片加载失败:', error);
            this.createDefaultCube(options);
          }
      );
    } catch (error) {
      console.error('创建静态图片模型失败:', error);
      this.createDefaultCube(options);
    }
  }

  /**
   * 创建一个简单的立方体作为最终备选
   */
  private createDefaultCube(options?: DigitalHumanOptions): void {
    try {
      // 创建一个简单的几何体
      const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
      const material = new THREE.MeshStandardMaterial({ color: 0x3498db });
      const cube = new THREE.Mesh(geometry, material);

      // 设置位置
      cube.position.set(0, 1.5, 0);

      // 添加到场景
      this.avatar = cube;
      this.scene.add(this.avatar);

      // 标记为已加载
      this.isLoaded = true;

      console.log('已创建立方体几何体作为数字人模型');

      if (options?.onLoad) options.onLoad();
    } catch (error) {
      console.error('创建默认立方体失败:', error);

      // 最后的备选：创建一个更简单的几何体
      try {
        const simpleGeometry = new THREE.SphereGeometry(0.5, 8, 8);
        const simpleMaterial = new THREE.MeshBasicMaterial({ color: 0x2980b9 });
        const sphere = new THREE.Mesh(simpleGeometry, simpleMaterial);

        sphere.position.set(0, 1.5, 0);
        this.avatar = sphere;
        this.scene.add(this.avatar);

        this.isLoaded = true;
        console.log('已创建球体几何体作为数字人模型');

        if (options?.onLoad) options.onLoad();
      } catch (finalError) {
        console.error('创建最终备选几何体失败:', finalError);
        if (options?.onError) options.onError(finalError);
      }
    }
  }

  /**
   * 创建本地缓存的静态模型
   * 这是一个方便的方法，用于在运行时直接创建模型而不是依赖预生成的文件
   */
  private createInMemoryModel(options?: DigitalHumanOptions): void {
    try {
      // 创建一个简单的人形模型
      const isMale = options?.gender === 'male';
      const group = new THREE.Group();

      // 身体 (圆柱体)
      const bodyGeometry = new THREE.CylinderGeometry(
          isMale ? 0.6 : 0.5,  // 顶部半径
          isMale ? 0.4 : 0.3,  // 底部半径
          isMale ? 1.7 : 1.5,  // 高度
          16                   // 分段
      );
      const bodyMaterial = new THREE.MeshStandardMaterial({
        color: isMale ? 0x4455aa : 0x7777cc
      });
      const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
      body.position.y = isMale ? 0.85 : 0.75;
      group.add(body);

      // 头部 (球体)
      const headGeometry = new THREE.SphereGeometry(
          isMale ? 0.45 : 0.4,  // 半径
          32,                   // 宽度分段
          32                    // 高度分段
      );
      const headMaterial = new THREE.MeshStandardMaterial({
        color: isMale ? 0xeebb99 : 0xffccaa
      });
      const head = new THREE.Mesh(headGeometry, headMaterial);
      head.position.y = isMale ? 2.0 : 1.85;
      group.add(head);

      // 眼睛
      const eyeGeometry = new THREE.SphereGeometry(0.05, 16, 16);
      const eyeMaterial = new THREE.MeshStandardMaterial({ color: 0x000000 });

      const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
      leftEye.position.set(0.15, isMale ? 2.05 : 1.9, 0.35);
      group.add(leftEye);

      const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
      rightEye.position.set(-0.15, isMale ? 2.05 : 1.9, 0.35);
      group.add(rightEye);

      // 嘴巴
      const mouthGeometry = new THREE.BoxGeometry(0.2, 0.02, 0.05);
      const mouthMaterial = new THREE.MeshStandardMaterial({
        color: isMale ? 0x993333 : 0xcc0000
      });
      const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
      mouth.position.set(0, isMale ? 1.85 : 1.75, 0.35);
      mouth.name = 'mouth'; // 为嘴巴命名，用于动画
      group.add(mouth);

      // 添加到场景
      this.avatar = group;
      this.scene.add(group);

      // 创建伪动画
      this.createPseudoAnimations(group);

      // 标记为已加载
      this.isLoaded = true;
      console.log('已创建内存中的简单人形模型');

      if (options?.onLoad) options.onLoad();
    } catch (error) {
      console.error('创建内存模型失败:', error);
      this.createDefaultCube(options);
    }
  }

  /**
   * 为内存模型创建伪动画
   */
  private createPseudoAnimations(model: THREE.Group): void {
    // 查找嘴巴对象
    const mouth = model.getObjectByName('mouth');

    if (mouth) {
      // 简化动画实现：使用自定义对象和动作而不是KeyframeTrack
      // 为每个状态创建一个简单的动作函数

      // 创建空闲动画动作
      const idleAction = {
        play: () => {
          // 空闲状态下，嘴巴保持正常
          mouth.scale.set(1, 1, 1);
          mouth.position.z = mouth.position.z;
        },
        stop: () => {},
        reset: () => { return idleAction; },
        fadeIn: () => { return idleAction; },
        fadeOut: () => { return idleAction; }
      };

      // 创建说话动画动作
      const talkAction = {
        play: () => {
          // 在动画循环中处理说话动作
          // 通过修改animate方法来实现嘴巴开合
          this.talkingMouth = mouth;
        },
        stop: () => {
          this.talkingMouth = null;
          mouth.scale.set(1, 1, 1);
        },
        reset: () => { return talkAction; },
        fadeIn: () => { return talkAction; },
        fadeOut: () => { return talkAction; }
      };

      // 创建听动画动作
      const listenAction = {
        play: () => {
          // 在动画循环中处理听动作
          this.listeningMouth = mouth;
        },
        stop: () => {
          this.listeningMouth = null;
          mouth.position.z = mouth.position.z;
        },
        reset: () => { return listenAction; },
        fadeIn: () => { return listenAction; },
        fadeOut: () => { return listenAction; }
      };

      // 存储动作
      this.animations.set(DigitalHumanState.IDLE, idleAction as any);
      this.animations.set(DigitalHumanState.TALKING, talkAction as any);
      this.animations.set(DigitalHumanState.LISTENING, listenAction as any);

      // 立即播放空闲动画
      this.playAnimation(DigitalHumanState.IDLE);
    }
  }

  // 添加临时属性以跟踪动画状态
  private talkingMouth: THREE.Object3D | null = null;
  private listeningMouth: THREE.Object3D | null = null;
  private animationTime: number = 0;
}

/**
 * 生成Ready Player Me头像URL
 */
export const generateReadyPlayerMeUrl = (options: {
  gender?: 'male' | 'female';
  outfit?: 'business' | 'casual';
  hairColor?: string;
}): string => {
  const gender = options.gender || 'female';
  const outfit = options.outfit || 'business';

  // 这里使用默认头像，实际应用中可以通过Ready Player Me API生成自定义头像
  return gender === 'male' ? DEFAULT_AVATARS[1] : DEFAULT_AVATARS[0];
}

/**
 * 创建数字人实例工厂函数
 */
export const createDigitalHuman = (container: HTMLElement, options?: {
  gender?: 'male' | 'female';
  customModelUrl?: string;
  onLoad?: () => void;
  onError?: (error: any) => void;
}): DigitalHuman => {
  return new DigitalHuman({
    container,
    gender: options?.gender || 'female',
    customModelUrl: options?.customModelUrl,
    onLoad: options?.onLoad,
    onError: options?.onError
  });
} 