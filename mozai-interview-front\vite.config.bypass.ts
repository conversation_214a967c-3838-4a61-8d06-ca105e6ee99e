import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 完全绕过 Element Plus ES 模块问题的配置
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      // 强制所有 Element Plus 引用使用 lib 版本
      'element-plus/es': 'element-plus/lib',
      'element-plus/es/': 'element-plus/lib/',
      'element-plus': 'element-plus/lib/index.js'
    },
    // 确保使用 Node.js 模块解析
    conditions: ['node', 'require', 'default'],
    mainFields: ['main', 'module', 'browser']
  },
  base: './',
  define: {
    global: 'globalThis',
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  },
  build: {
    outDir: 'dist-interview-ai',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    target: 'es2015',
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'vue-core': ['vue', 'vue-router', 'pinia'],
          'utils': ['axios', 'uuid']
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      },
      external: process.env.SKIP_OPTIONAL_DEPENDENCIES === 'true' ? ['ali-oss'] : []
    },
    chunkSizeWarningLimit: 1000,
    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true,
      // 强制将 Element Plus 作为 CommonJS 处理
      requireReturnsDefault: 'auto'
    }
  },
  server: {
    port: 3000,
    open: true,
    cors: true
  },
  optimizeDeps: {
    // 强制预构建 Element Plus 的 lib 版本
    include: [
      'element-plus/lib/index',
      'element-plus/lib/locale/lang/zh-cn'
    ],
    exclude: ['ali-oss'],
    esbuildOptions: {
      target: 'es2015'
    }
  }
})
