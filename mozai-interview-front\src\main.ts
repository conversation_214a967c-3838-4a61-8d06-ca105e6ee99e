import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import axios from 'axios'
import './utils/http' // 导入http工具
import { initUserAuth } from './utils/userAuth' // 导入用户认证系统

import './assets/main.css'

// 初始化用户认证系统
initUserAuth()

// 检查URL中是否包含SSO token (可能在不同位置)
const checkSSOToken = () => {
  const url = window.location.href
  console.log('检查URL中的SSO token:', url)

  // 检查URL中是否包含token参数 (在路径部分)
  const tokenMatch = url.match(/\/token=([^&/#]+)/)
  if (tokenMatch && tokenMatch[1]) {
    const token = tokenMatch[1]
    console.log('在URL路径中找到token:', token)
    localStorage.setItem('token', token)
    // 移除URL中的token参数，保留路径的其他部分
    const cleanUrl = url.replace(/\/token=[^&#]+/, '')
    if (cleanUrl !== url) {
      console.log('重定向到干净的URL:', cleanUrl)
      window.history.replaceState({}, document.title, cleanUrl)
    }
    return
  }
  
  // 未找到token
  console.log('URL中未找到token参数')
}

// 应用启动时检查SSO token
checkSSOToken()

// 检查补丁是否已应用
if ((window as any).TIMEOUT_PATCH_APPLIED) {
  console.log('【main.ts】检测到超时补丁已应用')
} else {
  console.log('【main.ts】超时补丁未应用，设置全局超时时间')
  // 全局配置axios默认超时为120秒
  axios.defaults.timeout = 120000;
  console.log('【main.ts】已配置axios全局超时时间为120秒')
}

// 输出当前axios配置
console.log('【main.ts】当前axios默认超时配置:', axios.defaults.timeout)

// 确保XMLHttpRequest超时设置
if (typeof XMLHttpRequest !== 'undefined') {
  console.log('【main.ts】配置XMLHttpRequest默认超时时间')
  const originalXhrOpen = XMLHttpRequest.prototype.open
  XMLHttpRequest.prototype.open = function(method: string, url: string | URL, async: boolean = true, username?: string | null, password?: string | null) {
    const result = originalXhrOpen.call(this, method, url, async, username, password)
    this.timeout = 120000 // 设置120秒超时
    console.log('【main.ts】设置XMLHttpRequest超时为120000ms')
    return result
  }
}

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app') 