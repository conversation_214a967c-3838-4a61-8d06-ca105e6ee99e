@echo off
echo 🚀 Element Plus 终极修复方案
echo Node.js 版本:
node --version

echo 🧹 清理构建环境...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist dist-interview-ai rmdir /s /q dist-interview-ai
if exist .vite rmdir /s /q .vite

echo 📦 安装依赖...
npm install

echo 🔧 修复 Element Plus 版本...
npm run fix:element

echo 🏗️ 尝试构建方式 1: Element Fix
npm run build:element-fix
if exist dist-interview-ai (
    echo ✅ 构建成功！
    goto success
)

echo ⚠️ 方式 1 失败，尝试方式 2...
if exist dist-interview-ai rmdir /s /q dist-interview-ai

echo 🏗️ 尝试构建方式 2: Legacy Config
npx vite build --config vite.config.legacy.ts
if exist dist-interview-ai (
    echo ✅ 构建成功！
    goto success
)

echo ⚠️ 方式 2 失败，尝试方式 3...
if exist dist-interview-ai rmdir /s /q dist-interview-ai

echo 🏗️ 尝试构建方式 3: Simple Build
npm run build:simple
if exist dist-interview-ai (
    echo ✅ 构建成功！
    goto success
)

echo ⚠️ 方式 3 失败，尝试方式 4...
if exist dist-interview-ai rmdir /s /q dist-interview-ai

echo 🏗️ 尝试构建方式 4: UMD Build
copy index.cdn.html index.html
npx vite build --config vite.config.umd.ts
if exist dist-interview-ai (
    echo ✅ 构建成功！
    goto success
)

echo ⚠️ 方式 4 失败，尝试最后方式...
if exist dist-interview-ai rmdir /s /q dist-interview-ai

echo 🏗️ 尝试构建方式 5: Force Build
set SKIP_OPTIONAL_DEPENDENCIES=true
npx vite build --force
if exist dist-interview-ai (
    echo ✅ 构建成功！
    goto success
)

echo ❌ 所有构建方式都失败了
echo.
echo 💡 建议的解决方案:
echo 1. 升级到 Node.js 18+
echo 2. 使用 yarn: yarn install ^&^& yarn build
echo 3. 手动下载 Element Plus UMD 版本
echo 4. 考虑使用其他 UI 库替代 Element Plus
echo 5. 联系开发团队获取帮助
pause
exit /b 1

:success
echo 🎉 构建成功完成！
echo 📁 构建产物:
dir dist-interview-ai
pause
