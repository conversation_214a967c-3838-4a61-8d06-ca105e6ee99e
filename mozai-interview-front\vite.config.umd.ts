import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 使用 UMD 版本的 Element Plus 配置
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  base: './',
  define: {
    global: 'globalThis',
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  },
  build: {
    outDir: 'dist-interview-ai',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    target: 'es2015',
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          'vue-core': ['vue', 'vue-router', 'pinia']
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      },
      // 将 Element Plus 作为外部依赖
      external: ['element-plus'].concat(
        process.env.SKIP_OPTIONAL_DEPENDENCIES === 'true' ? ['ali-oss'] : []
      ),
      // 全局变量映射
      globals: {
        'element-plus': 'ElementPlus'
      }
    },
    chunkSizeWarningLimit: 1000
  },
  server: {
    port: 3000,
    open: true,
    cors: true
  },
  optimizeDeps: {
    exclude: ['element-plus', 'ali-oss']
  }
})
