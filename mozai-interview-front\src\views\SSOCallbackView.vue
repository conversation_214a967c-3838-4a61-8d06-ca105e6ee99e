<template>
  <div class="sso-callback">
    <h1>处理SSO登录...</h1>
    <p>正在处理身份验证，请稍候...</p>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store'

const router = useRouter()
const userStore = useUserStore()

onMounted(async () => {
  console.log('SSO回调组件已加载')
  
  try {
    // 检查URL中的token，这里检查全部URL而不只是路由查询参数
    const url = window.location.href
    console.log('处理SSO回调URL:', url)
    
    // 检查不同的token位置模式
    let token: string | null = null
    
    // 模式1: 查询参数 ?token=xxx
    const queryMatch = url.match(/[?&]token=([^&#]+)/)
    if (queryMatch && queryMatch[1]) {
      token = queryMatch[1]
      console.log('在查询参数中找到token:', token)
    }
    
    // 模式2: URL路径部分 /token=xxx
    if (!token) {
      const pathMatch = url.match(/\/token=([^&#\/]+)/)
      if (pathMatch && pathMatch[1]) {
        token = pathMatch[1]
        console.log('在URL路径中找到token:', token)
      }
    }
    
    // 如果找到token，保存并进行用户信息获取
    if (token) {
      console.log('保存SSO回调中的token:', token)
      userStore.setToken(token)
      
      // 尝试获取用户信息
      console.log('使用token获取用户信息')
      await userStore.fetchUserInfo()
      
      // 跳转到首页
      console.log('跳转到首页')
      router.replace('/')
    } else {
      console.warn('SSO回调URL中未找到token')
      // 尝试获取用户信息，可能token在cookie中
      await userStore.fetchUserInfo()
      // 跳转到首页
      router.replace('/')
    }
  } catch (error) {
    console.error('处理SSO回调时出错:', error)
    // 出错时跳转到首页，让默认流程处理
    router.replace('/')
  }
})
</script>

<style scoped>
.sso-callback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
</style> 