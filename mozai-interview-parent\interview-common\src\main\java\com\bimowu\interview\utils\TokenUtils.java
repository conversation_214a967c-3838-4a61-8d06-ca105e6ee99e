package com.bimowu.interview.utils;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * Token工具类
 */
public class TokenUtils {

    /**
     * 获取当前请求中的用户ID
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Object userId = request.getAttribute("userId");
            if (userId != null) {
                return (Long) userId;
            }
        }
        return null;
    }

    /**
     * 获取当前请求中的客户端ID
     * @return 客户端ID
     */
    public static String getCurrentClientId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            Object clientId = request.getAttribute("clientId");
            if (clientId != null) {
                return (String) clientId;
            }
        }
        return null;
    }
    
    /**
     * 获取当前请求中的token
     * @return token
     */
    public static String getCurrentToken() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return request.getHeader("token");
        }
        return null;
    }
    
    /**
     * 移除当前用户ID（仅用于退出登录）
     * 注意：实际上我们不需要在请求中移除用户ID，因为用户ID是从token中获取的
     * 这个方法只是为了保持API的一致性
     */
    public static void removeCurrentUserId() {
        // 实际上，我们只需要在Redis中删除token对应的信息
        // 这个方法不需要做任何事情
    }
} 