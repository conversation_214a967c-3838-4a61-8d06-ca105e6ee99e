#!/bin/bash

echo "🚀 超级修复脚本 - 解决 Element Plus 模块问题"
echo "Node.js 版本: $(node --version)"

# 函数：显示进度
show_progress() {
    echo ""
    echo "🔄 $1..."
    echo "----------------------------------------"
}

# 1. 完全清理
show_progress "完全清理环境"
rm -rf node_modules package-lock.json dist-interview-ai .vite
npm cache clean --force

# 2. 安装依赖
show_progress "安装依赖"
npm install

# 3. 强制安装稳定版本的 Element Plus
show_progress "安装 Element Plus 2.0.6"
npm install element-plus@2.0.6 --save-exact

# 4. 修复模块引用
show_progress "修复 Element Plus 模块引用"
npm run fix:modules

# 5. 尝试最终构建
show_progress "开始构建"
npm run build:final

# 6. 检查结果
if [ -d "dist-interview-ai" ] && [ "$(ls -A dist-interview-ai)" ]; then
    echo ""
    echo "🎉 构建成功完成！"
    echo "📁 构建产物大小:"
    du -sh dist-interview-ai
    echo ""
    echo "📋 构建内容:"
    ls -la dist-interview-ai/
    echo ""
    echo "✅ 可以部署了！"
else
    echo ""
    echo "❌ 构建仍然失败"
    echo ""
    echo "🔧 手动尝试步骤:"
    echo "1. npm run fix:element"
    echo "2. npm run fix:modules" 
    echo "3. npx vite build --config vite.config.bypass.ts"
    echo ""
    echo "💡 或者考虑:"
    echo "- 升级到 Node.js 18+"
    echo "- 使用 yarn 替代 npm"
    echo "- 联系开发团队"
    exit 1
fi
