<template>
  <div class="end-container">
    <div class="end-content">
      <el-result
        icon="success"
        title="面试已完成"
        sub-title="感谢您完成本次AI面试"
      >
        <template #extra>
          <div class="result-info">
            <el-alert
              title="面试结果正在生成中"
              type="info"
              :closable="false"
              show-icon
            >
              <p class="alert-content">
                系统正在分析您的面试表现，生成详细的面试报告。<br>
                请稍后前往面试记录查看您的面试结果。
              </p>
            </el-alert>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goToInterviewList">
              查看面试记录
            </el-button>
            <el-button @click="goToHome">返回首页</el-button>
          </div>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToInterviewList = () => {
  router.push('/interviews')
}

const goToHome = () => {
  router.push('/')
}
</script>

<style scoped>
.end-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.end-content {
  background-color: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
}

.result-info {
  margin: 20px 0;
}

.alert-content {
  margin: 10px 0;
  line-height: 1.6;
  color: #606266;
}

.action-buttons {
  margin-top: 30px;
  display: flex;
  gap: 16px;
  justify-content: center;
}

:deep(.el-result__icon) {
  margin-bottom: 20px;
}

:deep(.el-result__title) {
  margin-top: 20px;
  font-size: 24px;
}

:deep(.el-result__subtitle) {
  margin-top: 10px;
  font-size: 16px;
}
</style> 