<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0236604f-1493-44c8-96c4-30a1fe366ea2" name="Changes" comment="node依赖冲突" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2ykoKcCS1afZzhgUvQy3BzJQyWs" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/IdeaProjects/sso/interview/mozai-interview-front&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\IdeaProjects\\sso\\interview\\mozai-interview-front\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\sso\interview\mozai-interview-front\public\imgs" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\interview\mozai-interview-front\src\assets" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\sso\interview\mozai-interview-front\public\models" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0236604f-1493-44c8-96c4-30a1fe366ea2" name="Changes" comment="" />
      <created>1750387868395</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750387868395</updated>
      <workItem from="1750387869558" duration="3384000" />
      <workItem from="1750406473947" duration="46000" />
      <workItem from="1751523041057" duration="2144000" />
      <workItem from="1751528103988" duration="8284000" />
      <workItem from="1751594184382" duration="17381000" />
      <workItem from="1752050825857" duration="25093000" />
      <workItem from="1752208302935" duration="7345000" />
      <workItem from="1752225839200" duration="2471000" />
      <workItem from="1752562823624" duration="4164000" />
      <workItem from="1752656180786" duration="11266000" />
      <workItem from="1752978742228" duration="595000" />
      <workItem from="1753152025207" duration="600000" />
      <workItem from="1754290140463" duration="3353000" />
      <workItem from="1754449966436" duration="595000" />
      <workItem from="1754878596689" duration="1824000" />
      <workItem from="1755136668259" duration="594000" />
      <workItem from="1755484111586" duration="1701000" />
    </task>
    <task id="LOCAL-00044" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752128884960</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1752128884960</updated>
    </task>
    <task id="LOCAL-00045" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752128954373</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1752128954373</updated>
    </task>
    <task id="LOCAL-00046" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752129541647</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1752129541647</updated>
    </task>
    <task id="LOCAL-00047" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752130603927</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1752130603927</updated>
    </task>
    <task id="LOCAL-00048" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752143420539</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1752143420539</updated>
    </task>
    <task id="LOCAL-00049" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752143955557</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1752143955557</updated>
    </task>
    <task id="LOCAL-00050" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752144062702</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1752144062702</updated>
    </task>
    <task id="LOCAL-00051" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752145008215</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1752145008215</updated>
    </task>
    <task id="LOCAL-00052" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752145395695</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1752145395695</updated>
    </task>
    <task id="LOCAL-00053" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752145989715</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1752145989715</updated>
    </task>
    <task id="LOCAL-00054" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752146283244</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1752146283244</updated>
    </task>
    <task id="LOCAL-00055" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752199510669</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1752199510670</updated>
    </task>
    <task id="LOCAL-00056" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752202259460</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1752202259460</updated>
    </task>
    <task id="LOCAL-00057" summary="oss-前端上传--------">
      <option name="closed" value="true" />
      <created>1752202640107</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1752202640107</updated>
    </task>
    <task id="LOCAL-00058" summary="面试bug">
      <option name="closed" value="true" />
      <created>1752208323369</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1752208323369</updated>
    </task>
    <task id="LOCAL-00059" summary="面试bug">
      <option name="closed" value="true" />
      <created>1752213822345</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1752213822346</updated>
    </task>
    <task id="LOCAL-00060" summary="面试bug">
      <option name="closed" value="true" />
      <created>1752214895512</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1752214895512</updated>
    </task>
    <task id="LOCAL-00061" summary="面试bug">
      <option name="closed" value="true" />
      <created>1752215301260</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1752215301261</updated>
    </task>
    <task id="LOCAL-00062" summary="面试bug">
      <option name="closed" value="true" />
      <created>1752215784466</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1752215784466</updated>
    </task>
    <task id="LOCAL-00063" summary="面试优化">
      <option name="closed" value="true" />
      <created>1752218639784</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1752218639784</updated>
    </task>
    <task id="LOCAL-00064" summary="面试优化">
      <option name="closed" value="true" />
      <created>1752219038047</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1752219038047</updated>
    </task>
    <task id="LOCAL-00065" summary="sso回调问题">
      <option name="closed" value="true" />
      <created>1752224126933</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1752224126933</updated>
    </task>
    <task id="LOCAL-00066" summary="优化">
      <option name="closed" value="true" />
      <created>1752226173057</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1752226173057</updated>
    </task>
    <task id="LOCAL-00067" summary="优化">
      <option name="closed" value="true" />
      <created>1752226449708</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1752226449708</updated>
    </task>
    <task id="LOCAL-00068" summary="优化">
      <option name="closed" value="true" />
      <created>1752230779069</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1752230779069</updated>
    </task>
    <task id="LOCAL-00069" summary="面试结果字段">
      <option name="closed" value="true" />
      <created>1752564083459</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1752564083459</updated>
    </task>
    <task id="LOCAL-00070" summary="面试结果字段">
      <option name="closed" value="true" />
      <created>1752565613689</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1752565613689</updated>
    </task>
    <task id="LOCAL-00071" summary="回滚">
      <option name="closed" value="true" />
      <created>1752565884295</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1752565884295</updated>
    </task>
    <task id="LOCAL-00072" summary="面试结果展示">
      <option name="closed" value="true" />
      <created>1752566383417</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1752566383417</updated>
    </task>
    <task id="LOCAL-00073" summary="添加日志信息">
      <option name="closed" value="true" />
      <created>1752656329032</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1752656329032</updated>
    </task>
    <task id="LOCAL-00074" summary="模拟面试视频上传">
      <option name="closed" value="true" />
      <created>1752720909505</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1752720909505</updated>
    </task>
    <task id="LOCAL-00075" summary="3d数字人优化">
      <option name="closed" value="true" />
      <created>1752736339645</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1752736339645</updated>
    </task>
    <task id="LOCAL-00076" summary="3d数字人优化">
      <option name="closed" value="true" />
      <created>1752736868053</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1752736868054</updated>
    </task>
    <task id="LOCAL-00077" summary="3d数字人优化-----回滚">
      <option name="closed" value="true" />
      <created>1752737366034</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1752737366034</updated>
    </task>
    <task id="LOCAL-00078" summary="3d数字人优化-----1">
      <option name="closed" value="true" />
      <created>1752742753247</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1752742753247</updated>
    </task>
    <task id="LOCAL-00079" summary="3d数字人优化-----2">
      <option name="closed" value="true" />
      <created>1752742982733</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1752742982733</updated>
    </task>
    <task id="LOCAL-00080" summary="3d数字人优化-----3">
      <option name="closed" value="true" />
      <created>1752743210764</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1752743210764</updated>
    </task>
    <task id="LOCAL-00081" summary="3d数字人优化-----4">
      <option name="closed" value="true" />
      <created>1752743532104</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1752743532104</updated>
    </task>
    <task id="LOCAL-00082" summary="3d数字人优化-----5">
      <option name="closed" value="true" />
      <created>1752743667553</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1752743667553</updated>
    </task>
    <task id="LOCAL-00083" summary="3d数字人优化-----6">
      <option name="closed" value="true" />
      <created>1752743867738</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1752743867738</updated>
    </task>
    <task id="LOCAL-00084" summary="3d数字人优化-----回滚">
      <option name="closed" value="true" />
      <created>1752744033697</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1752744033697</updated>
    </task>
    <task id="LOCAL-00085" summary="3d数字人优化-----回滚">
      <option name="closed" value="true" />
      <created>1752744141929</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1752744141929</updated>
    </task>
    <task id="LOCAL-00086" summary="系统名称优化">
      <option name="closed" value="true" />
      <created>1754290201650</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1754290201651</updated>
    </task>
    <task id="LOCAL-00087" summary="系统名称优化">
      <option name="closed" value="true" />
      <created>1754299360758</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1754299360758</updated>
    </task>
    <task id="LOCAL-00088" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755484334511</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1755484334511</updated>
    </task>
    <task id="LOCAL-00089" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755484433793</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1755484433793</updated>
    </task>
    <task id="LOCAL-00090" summary="node依赖冲突">
      <option name="closed" value="true" />
      <created>1755485455800</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1755485455800</updated>
    </task>
    <task id="LOCAL-00091" summary="node依赖冲突">
      <option name="closed" value="true" />
      <created>1755485893897</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1755485893897</updated>
    </task>
    <task id="LOCAL-00092" summary="node依赖冲突">
      <option name="closed" value="true" />
      <created>1755486171511</created>
      <option name="number" value="00092" />
      <option name="presentableId" value="LOCAL-00092" />
      <option name="project" value="LOCAL" />
      <updated>1755486171512</updated>
    </task>
    <option name="localTasksCounter" value="93" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="cf194388-972c-4561-a112-8cdfdc0044a5" value="TOOL_WINDOW" />
        <entry key="2631cfa9-1c45-4570-acd1-6ad0a22f9994" value="TOOL_WINDOW" />
        <entry key="c24b8236-0e53-4331-b3bf-cbd1181a6ec7" value="TOOL_WINDOW" />
        <entry key="78fb0d38-10e3-40a8-a805-c19056691f97" value="TOOL_WINDOW" />
        <entry key="08c58581-349a-4fe6-a751-88556e002a48" value="TOOL_WINDOW" />
        <entry key="ebab154a-3a9c-4211-9667-17d500cb8102" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="08c58581-349a-4fe6-a751-88556e002a48">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="2631cfa9-1c45-4570-acd1-6ad0a22f9994">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="78fb0d38-10e3-40a8-a805-c19056691f97">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
        <entry key="c24b8236-0e53-4331-b3bf-cbd1181a6ec7">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="cf194388-972c-4561-a112-8cdfdc0044a5">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="ebab154a-3a9c-4211-9667-17d500cb8102">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="退出登录" />
    <MESSAGE value="优化-我的面试跳转" />
    <MESSAGE value="oss-前端上传" />
    <MESSAGE value="oss-前端上传--------回滚" />
    <MESSAGE value="oss-前端上传--------" />
    <MESSAGE value="面试bug" />
    <MESSAGE value="面试优化" />
    <MESSAGE value="sso回调问题" />
    <MESSAGE value="优化" />
    <MESSAGE value="面试结果字段" />
    <MESSAGE value="回滚" />
    <MESSAGE value="面试结果展示" />
    <MESSAGE value="添加日志信息" />
    <MESSAGE value="模拟面试视频上传" />
    <MESSAGE value="3d数字人优化" />
    <MESSAGE value="3d数字人优化-----1" />
    <MESSAGE value="3d数字人优化-----2" />
    <MESSAGE value="3d数字人优化-----3" />
    <MESSAGE value="3d数字人优化-----4" />
    <MESSAGE value="3d数字人优化-----5" />
    <MESSAGE value="3d数字人优化-----6" />
    <MESSAGE value="3d数字人优化-----回滚" />
    <MESSAGE value="系统名称优化" />
    <MESSAGE value="生产环境" />
    <MESSAGE value="node依赖冲突" />
    <option name="LAST_COMMIT_MESSAGE" value="node依赖冲突" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/utils/http.ts</url>
          <properties lambdaOrdinal="-1" />
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>