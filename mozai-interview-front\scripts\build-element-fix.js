const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Element Plus 兼容性修复构建');

async function fixElementPlusModules() {
  const elementPlusPath = path.join(__dirname, '../node_modules/element-plus');
  
  if (!fs.existsSync(elementPlusPath)) {
    console.log('⚠️ Element Plus 未安装，跳过模块修复');
    return;
  }
  
  console.log('🔨 修复 Element Plus 模块引用...');
  
  try {
    // 检查问题文件是否存在
    const indexPath = path.join(elementPlusPath, 'es/index.mjs');
    if (fs.existsSync(indexPath)) {
      let content = fs.readFileSync(indexPath, 'utf8');
      
      // 替换有问题的模块引用
      content = content.replace(
        /\.\/hooks\/use-prevent-globalThis\/index\.mjs/g,
        './hooks/use-global-config/index.mjs'
      );
      content = content.replace(
        /\.\/components\/config-provider\/src\/hooks\/use-globalThis-config\.mjs/g,
        './components/config-provider/src/hooks/use-global-config.mjs'
      );
      
      fs.writeFileSync(indexPath, content);
      console.log('✅ Element Plus 模块引用已修复');
    }
  } catch (error) {
    console.log('⚠️ 模块修复失败，将使用备用方案:', error.message);
  }
}

async function main() {
  try {
    // 1. 清理构建目录
    console.log('🧹 清理构建目录...');
    if (fs.existsSync('dist-interview-ai')) {
      fs.rmSync('dist-interview-ai', { recursive: true, force: true });
    }
    
    // 2. 修复 Element Plus 模块
    await fixElementPlusModules();
    
    // 3. 设置环境变量
    const env = {
      ...process.env,
      NODE_OPTIONS: '--max-old-space-size=4096',
      SKIP_OPTIONAL_DEPENDENCIES: 'true',
      VITE_LEGACY_BUILD: 'true'
    };
    
    // 4. 尝试多种构建方式
    const buildConfigs = [
      'vite.config.legacy.ts',
      'vite.config.node16.ts',
      'vite.config.ts'
    ];
    
    for (const config of buildConfigs) {
      if (!fs.existsSync(config)) continue;
      
      console.log(`🏗️ 尝试使用配置: ${config}`);
      try {
        execSync(`npx vite build --config ${config}`, { 
          stdio: 'inherit',
          env
        });
        console.log('✅ 构建成功！');
        return;
      } catch (error) {
        console.log(`⚠️ 配置 ${config} 构建失败，尝试下一个...`);
      }
    }
    
    // 5. 最后的备用方案：强制使用 CommonJS
    console.log('🏗️ 使用最后的备用方案...');
    execSync('npx vite build --force --mode production', { 
      stdio: 'inherit',
      env: {
        ...env,
        VITE_FORCE_CJS: 'true'
      }
    });
    
    console.log('✅ 构建完成！');
    
  } catch (error) {
    console.error('❌ 所有构建方式都失败了:', error.message);
    
    console.log('\n💡 建议的解决方案:');
    console.log('1. 手动安装稳定版本: npm install element-plus@2.1.11 --save-exact');
    console.log('2. 清理并重装: rm -rf node_modules package-lock.json && npm install');
    console.log('3. 使用 yarn: yarn install && yarn build');
    console.log('4. 升级到 Node.js 18+');
    console.log('5. 考虑使用 Element Plus 的 CDN 版本');
    
    process.exit(1);
  }
}

main();
