import { ElMessage } from 'element-plus';

/**
 * 检查人脸检测模型文件是否存在
 * @returns Promise<boolean> 模型文件是否全部存在
 */
export async function checkFaceDetectionModels(): Promise<boolean> {
  const requiredModels = [
    '/models/tiny_face_detector_model-weights_manifest.json',
    '/models/face_landmark_68_model-weights_manifest.json',
    '/models/face_expression_model-weights_manifest.json'
  ];
  
  try {
    const results = await Promise.all(
      requiredModels.map(async (modelPath) => {
        try {
          const response = await fetch(modelPath, { method: 'HEAD' });
          return response.ok;
        } catch (error) {
          console.error(`检查模型文件失败: ${modelPath}`, error);
          return false;
        }
      })
    );
    
    const allModelsExist = results.every(result => result);
    
    if (!allModelsExist) {
      ElMessage.error({
        message: '人脸检测模型文件缺失，部分功能可能无法正常工作。请联系管理员解决。',
        duration: 8000
      });
      console.error('缺少以下模型文件:', 
        requiredModels.filter((_, index) => !results[index])
      );
    }
    
    return allModelsExist;
  } catch (error) {
    console.error('检查模型文件时发生错误:', error);
    return false;
  }
}

/**
 * 加载人脸检测模型
 * @param faceapi face-api.js 实例
 * @returns Promise<void>
 */
export async function loadFaceDetectionModels(faceapi: any): Promise<void> {
  try {
    // 先检查模型文件是否存在
    const modelsExist = await checkFaceDetectionModels();
    
    if (!modelsExist) {
      console.warn('部分模型文件缺失，尝试加载可用模型');
    }
    
    // 尝试加载模型
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri('/models'),
      faceapi.nets.faceLandmark68Net.loadFromUri('/models'),
      faceapi.nets.faceExpressionNet.loadFromUri('/models')
    ]);
    
    console.log('人脸检测模型加载成功');
  } catch (error) {
    console.error('加载人脸检测模型失败:', error);
    ElMessage.error({
      message: '人脸检测模型加载失败，请刷新页面重试或联系管理员。',
      duration: 8000
    });
    throw error;
  }
} 