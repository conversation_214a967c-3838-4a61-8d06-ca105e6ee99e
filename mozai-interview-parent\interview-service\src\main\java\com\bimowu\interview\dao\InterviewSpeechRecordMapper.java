package com.bimowu.interview.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.interview.model.InterviewSpeechRecord;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 面试语音转文字记录Mapper接口
 */
@Mapper
public interface InterviewSpeechRecordMapper extends BaseMapper<InterviewSpeechRecord> {

    /**
     * 插入语音记录
     * 
     * @param record 语音记录
     * @return 影响的行数
     */
    int insert(InterviewSpeechRecord record);
    
    /**
     * 更新语音记录
     * 
     * @param record 语音记录
     * @return 影响的行数
     */
    int update(InterviewSpeechRecord record);
    
    /**
     * 根据ID查询语音记录
     * 
     * @param id 记录ID
     * @return 语音记录
     */
    InterviewSpeechRecord selectById(@Param("id") Long id);
    
    /**
     * 根据面试ID查询语音记录列表
     * 
     * @param interviewId 面试ID
     * @return 语音记录列表
     */
    List<InterviewSpeechRecord> selectByInterviewId(@Param("interviewId") String interviewId);
    
    /**
     * 根据面试ID和时间范围查询语音记录
     * 
     * @param interviewId 面试ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 语音记录列表
     */
    List<InterviewSpeechRecord> selectByTimeRange(
            @Param("interviewId") String interviewId,
            @Param("startTime") Integer startTime,
            @Param("endTime") Integer endTime);
    
    /**
     * 根据面试ID和发言人ID查询语音记录
     * 
     * @param interviewId 面试ID
     * @param speakerId 发言人ID
     * @return 语音记录列表
     */
    List<InterviewSpeechRecord> selectBySpeaker(
            @Param("interviewId") String interviewId,
            @Param("speakerId") Integer speakerId);
    
    /**
     * 根据条件查询语音记录列表
     * 
     * @param params 查询条件
     * @return 语音记录列表
     */
    List<InterviewSpeechRecord> selectByCondition(Map<String, Object> params);
    
    /**
     * 根据ID删除语音记录
     * 
     * @param id 记录ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据面试ID删除所有语音记录
     * 
     * @param interviewId 面试ID
     * @return 影响的行数
     */
    int deleteByInterviewId(@Param("interviewId") String interviewId);
} 