@echo off
echo 🧪 测试 Linux Node.js 16 构建配置
echo 当前 Node.js 版本:
node --version

echo.
echo 📦 清理并重新安装依赖...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist dist-interview-ai rmdir /s /q dist-interview-ai
if exist .vite rmdir /s /q .vite

npm cache clean --force
npm install

echo.
echo 🏗️ 测试 Node.js 16 兼容构建...
set NODE_OPTIONS=--max-old-space-size=4096
set SKIP_OPTIONAL_DEPENDENCIES=true
npm run build:node16

echo.
if exist dist-interview-ai (
    echo ✅ 构建测试成功！
    echo 📁 构建产物:
    dir dist-interview-ai
    echo.
    echo 🐧 在 Linux 服务器上运行以下命令:
    echo git pull
    echo chmod +x fix-linux-build.sh
    echo ./fix-linux-build.sh
) else (
    echo ❌ 构建测试失败
    echo.
    echo 💡 可能的问题:
    echo 1. Node.js 版本不兼容
    echo 2. 依赖安装失败
    echo 3. 系统缺少构建工具
)

pause
