import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 极度兼容的 Node.js 16 配置
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      // 强制使用 Element Plus 的 lib 版本而不是 es 版本
      'element-plus/es': 'element-plus/lib',
      'element-plus': 'element-plus/lib'
    },
    // 确保模块解析使用 Node.js 方式
    conditions: ['node', 'default'],
    mainFields: ['main', 'module']
  },
  base: './',
  define: {
    global: 'globalThis',
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  },
  build: {
    outDir: 'dist-interview-ai',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    target: 'es2015',
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          'element-ui': ['element-plus'],
          'vue-core': ['vue', 'vue-router', 'pinia']
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      },
      external: process.env.SKIP_OPTIONAL_DEPENDENCIES === 'true' ? ['ali-oss'] : []
    },
    chunkSizeWarningLimit: 1000,
    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true
    }
  },
  server: {
    port: 3000,
    open: true,
    cors: true
  },
  optimizeDeps: {
    // 强制预构建 Element Plus
    include: [
      'element-plus/lib/index',
      'element-plus/lib/locale/lang/zh-cn'
    ],
    exclude: ['ali-oss'],
    // 强制使用 esbuild
    esbuildOptions: {
      target: 'es2015'
    }
  },
  // CSS 处理
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "element-plus/theme-chalk/src/index.scss" as *;`
      }
    }
  }
})
