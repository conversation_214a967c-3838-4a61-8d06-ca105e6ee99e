<template>
  <div ref="container" class="three-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'

const container = ref<HTMLDivElement | null>(null)
let renderer: THREE.WebGLRenderer
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let animationId: number

onMounted(() => {
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0x000000)

  camera = new THREE.PerspectiveCamera(45, container.value!.clientWidth / container.value!.clientHeight, 0.1, 1000)
  camera.position.set(0, 1.6, 3)

  renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
  renderer.setSize(container.value!.clientWidth, container.value!.clientHeight)
  container.value!.appendChild(renderer.domElement)

  const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444, 1.2)
  hemiLight.position.set(0, 20, 0)
  scene.add(hemiLight)
  const dirLight = new THREE.DirectionalLight(0xffffff, 0.8)
  dirLight.position.set(3, 10, 10)
  scene.add(dirLight)

  const loader = new GLTFLoader()
  loader.load('/models/digital-human.glb', (gltf) => {
    const model = gltf.scene
    model.position.set(0, 0, 0)
    model.scale.set(1.2, 1.2, 1.2)
    scene.add(model)
  })

  const animate = () => {
    renderer.render(scene, camera)
    animationId = requestAnimationFrame(animate)
  }
  animate()
})

onBeforeUnmount(() => {
  cancelAnimationFrame(animationId)
  renderer.dispose()
  scene.clear()
})
</script>

<style scoped>
.three-container {
  width: 100%;
  height: 400px;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
}
</style> 