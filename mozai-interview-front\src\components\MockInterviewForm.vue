<template>
  <div class="mock-interview-form">
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="面试岗位" prop="position">
        <el-select v-model="formData.position" placeholder="请选择面试岗位">
          <el-option label="开发" :value="1"></el-option>
          <el-option label="技术支持" :value="2"></el-option>
          <el-option label="测试" :value="3"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="面试时间" prop="interviewTime">
        <CustomDatePicker 
          v-model="formData.interviewTime" 
          placeholder="请选择面试时间"
          @change="handleCustomDateChange"
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, FormInstance } from 'element-plus'
import { createMockInterview } from '@/api/interview'
import type { MockInterview } from '@/types/interview'
import { InterviewType } from '@/types/interview'
import { formatDate } from '@/utils/dateFormat'
import CustomDatePicker from '@/components/CustomDatePicker.vue'

// 定义事件
const emit = defineEmits(['success'])

// 表单引用
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  position: '',
  interviewTime: ''
})

// 表单验证规则
const rules = {
  position: [
    { required: true, message: '请选择面试岗位', trigger: 'change' }
  ],
  interviewTime: [
    { required: true, message: '请选择面试时间', trigger: 'change' }
  ]
}

// 组件挂载时设置默认日期
onMounted(() => {
  // 设置默认日期为当前时间
  const now = new Date();
  formData.interviewTime = formatDate(now.toString());
})

// 处理自定义日期选择器变更
const handleCustomDateChange = (val: string) => {
  console.log('自定义日期选择器变更:', val);
  formData.interviewTime = val;
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 创建模拟面试记录
        const interviewData: Partial<MockInterview> = {
          type: InterviewType.MOCK,
          position: formData.position,
          interviewTime: formData.interviewTime
        }
        
        const interviewId = await createMockInterview(interviewData)
        
        if (!interviewId) {
          ElMessage.error('创建面试记录失败')
          return
        }
        
        ElMessage.success('模拟面试记录创建成功')
        resetForm()
        emit('success')
      } catch (error) {
        console.error('提交面试记录失败:', error)
        ElMessage.error('提交失败，请稍后重试')
      }
    } else {
      ElMessage.warning('请完善表单信息')
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.mock-interview-form {
  padding: 20px;
}

.date-picker-wrapper {
  position: relative;
}

.formatted-date {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 5px;
  padding: 5px 10px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}
</style> 