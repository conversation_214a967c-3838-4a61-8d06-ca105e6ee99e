package com.bimowu.interview.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 简历知识管理实体类
 */
@Data
public class ResumeKnowledge {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 职位类别
     * 1：开发
     * 2：技术支持
     */
    private Integer positionType;
    
    /**
     * 知识目录（1.1，1.2等）
     */
    private String knowledgeCatalog;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 删除标识（0：未删除；1：已删除）
     */
    private Integer isDeleted;
} 