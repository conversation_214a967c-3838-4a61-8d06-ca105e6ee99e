/**
 * 该脚本用于生成简单的3D模型文件
 * 使用three.js的GLTFExporter导出GLTF/GLB格式文件
 */
const fs = require('fs');
const path = require('path');
const THREE = require('three');
const { GLTFExporter } = require('three/examples/jsm/exporters/GLTFExporter');

// 确保输出目录存在
const outputDir = path.join(__dirname, '../public/models');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 创建简单的女性数字人模型（使用简单几何体）
function createFemaleModel() {
  const scene = new THREE.Scene();
  
  // 创建一个组合体来表示"人"
  const group = new THREE.Group();
  
  // 身体（圆柱体）
  const bodyGeometry = new THREE.CylinderGeometry(0.5, 0.3, 1.5, 16);
  const bodyMaterial = new THREE.MeshStandardMaterial({ color: 0x7777cc });
  const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
  body.position.y = 0.75;
  group.add(body);
  
  // 头部（球体）
  const headGeometry = new THREE.SphereGeometry(0.4, 32, 32);
  const headMaterial = new THREE.MeshStandardMaterial({ color: 0xffccaa });
  const head = new THREE.Mesh(headGeometry, headMaterial);
  head.position.y = 1.85;
  group.add(head);
  
  // 添加眼睛
  const eyeGeometry = new THREE.SphereGeometry(0.05, 16, 16);
  const eyeMaterial = new THREE.MeshStandardMaterial({ color: 0x000000 });
  
  const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
  leftEye.position.set(0.15, 1.9, 0.35);
  group.add(leftEye);
  
  const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
  rightEye.position.set(-0.15, 1.9, 0.35);
  group.add(rightEye);
  
  // 嘴巴（简单的线条）
  const mouthGeometry = new THREE.BoxGeometry(0.2, 0.02, 0.05);
  const mouthMaterial = new THREE.MeshStandardMaterial({ color: 0xcc0000 });
  const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
  mouth.position.set(0, 1.75, 0.35);
  group.add(mouth);
  
  // 添加组合体到场景
  scene.add(group);
  
  // 添加灯光
  const light = new THREE.DirectionalLight(0xffffff, 1);
  light.position.set(1, 1, 1);
  scene.add(light);
  
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
  scene.add(ambientLight);
  
  return scene;
}

// 创建简单的男性数字人模型
function createMaleModel() {
  const scene = new THREE.Scene();
  
  // 创建一个组合体来表示"人"
  const group = new THREE.Group();
  
  // 身体（圆柱体）
  const bodyGeometry = new THREE.CylinderGeometry(0.6, 0.4, 1.7, 16);
  const bodyMaterial = new THREE.MeshStandardMaterial({ color: 0x4455aa });
  const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
  body.position.y = 0.85;
  group.add(body);
  
  // 头部（球体）
  const headGeometry = new THREE.SphereGeometry(0.45, 32, 32);
  const headMaterial = new THREE.MeshStandardMaterial({ color: 0xeebb99 });
  const head = new THREE.Mesh(headGeometry, headMaterial);
  head.position.y = 2.0;
  group.add(head);
  
  // 添加眼睛
  const eyeGeometry = new THREE.SphereGeometry(0.05, 16, 16);
  const eyeMaterial = new THREE.MeshStandardMaterial({ color: 0x000000 });
  
  const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
  leftEye.position.set(0.15, 2.05, 0.4);
  group.add(leftEye);
  
  const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
  rightEye.position.set(-0.15, 2.05, 0.4);
  group.add(rightEye);
  
  // 嘴巴（简单的线条）
  const mouthGeometry = new THREE.BoxGeometry(0.2, 0.02, 0.05);
  const mouthMaterial = new THREE.MeshStandardMaterial({ color: 0x993333 });
  const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
  mouth.position.set(0, 1.85, 0.4);
  group.add(mouth);
  
  // 添加组合体到场景
  scene.add(group);
  
  // 添加灯光
  const light = new THREE.DirectionalLight(0xffffff, 1);
  light.position.set(1, 1, 1);
  scene.add(light);
  
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
  scene.add(ambientLight);
  
  return scene;
}

// 导出模型函数
function exportGLTF(scene, fileName) {
  return new Promise((resolve, reject) => {
    const exporter = new GLTFExporter();
    const options = {
      binary: true,  // 使用GLB格式，更紧凑
      animations: [],
      onlyVisible: true
    };
    
    exporter.parse(
      scene,
      (result) => {
        // 结果可能是ArrayBuffer（二进制GLB）或JSON（GLTF）
        if (result instanceof ArrayBuffer) {
          fs.writeFileSync(path.join(outputDir, fileName), Buffer.from(result));
          console.log(`已导出GLB文件: ${fileName}`);
        } else {
          const output = JSON.stringify(result, null, 2);
          fs.writeFileSync(path.join(outputDir, fileName.replace('.glb', '.gltf')), output);
          console.log(`已导出GLTF文件: ${fileName.replace('.glb', '.gltf')}`);
        }
        resolve();
      },
      (error) => {
        console.error('导出GLTF时出错:', error);
        reject(error);
      },
      options
    );
  });
}

// 主函数
async function main() {
  try {
    // 生成女性模型
    const femaleScene = createFemaleModel();
    await exportGLTF(femaleScene, 'digital-human-female.glb');
    
    // 生成男性模型
    const maleScene = createMaleModel();
    await exportGLTF(maleScene, 'digital-human-male.glb');
    
    console.log('所有模型已成功生成！');
  } catch (error) {
    console.error('生成模型时出错:', error);
  }
}

// 执行主函数
main(); 