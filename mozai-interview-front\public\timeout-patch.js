/**
 * axios超时补丁 
 * 在Axios加载之前应用此补丁
 */
console.log('【超时补丁】加载超时设置补丁');

// 保存原始XHR
var OriginalXHR = window.XMLHttpRequest;

// 劫持XHR构造函数
window.XMLHttpRequest = function() {
  var xhr = new OriginalXHR();
  
  // 在创建时就设置超时为120秒
  xhr.timeout = 120000;
  console.log('【超时补丁】新建XMLHttpRequest，设置超时为120000ms');
  
  // 保存原始open方法
  var originalOpen = xhr.open;
  
  // 重写open方法，确保超时时间不被覆盖
  xhr.open = function() {
    var result = originalOpen.apply(xhr, arguments);
    xhr.timeout = 120000; // 再次确保超时设置
    return result;
  };
  
  return xhr;
};

// 复制原型链确保继承关系
window.XMLHttpRequest.prototype = OriginalXHR.prototype;

// 告知应用补丁状态
window.TIMEOUT_PATCH_APPLIED = true;
console.log('【超时补丁】超时设置补丁已加载完成'); 