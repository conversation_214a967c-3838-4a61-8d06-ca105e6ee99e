import http from '../utils/http'

// 定义API响应类型
export interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// API接口
export default {
  getResumeCategoryList() {
    return http.get('/resume/categoryList')
  },

  getInterviewQuestions(id: string) {
    return http.get(`/interviews/${id}/questions`)
  },

  // 简历分析
  analyzeResume(file: File): Promise<BaseResponse<string[]>> {
    const formData = new FormData()
    formData.append('file', file)

    return http.post('/resume/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 创建面试记录
  createInterview(interviewData: {
    candidateName: string,
    position: string,
    stage: string,
    experience: string,
    questions: string[],
    type?: string  // 添加可选的type字段
  }): Promise<BaseResponse<{ interviewId: string }>> {
    // 确保设置type字段为'mock'(模拟面试)
    const data = {
      ...interviewData,
      type: interviewData.type || 'mock'  // 如果未指定，默认为模拟面试
    };

    return http.post('/interviews', data)
  },

  // 生成面试问题
  generateQuestions(resumeText: string) {
    return http.post('/interview/questions', { resumeText }, {
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 获取数字人模型列表
  getDigitalHumanModels() {
    return http.get('/digital-human/models', {
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 分析面试表现
  analyzeInterview(data: {
    video: Blob,
    audio: Blob,
    questions: string[],
    answers: string[]
  }) {
    const formData = new FormData()
    formData.append('video', data.video)
    formData.append('audio', data.audio)
    formData.append('questions', JSON.stringify(data.questions))
    formData.append('answers', JSON.stringify(data.answers))

    return http.post('/interview/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 语音转文字
  speechToText(audio: Blob) {
    const formData = new FormData()
    formData.append('audio', audio)

    return http.post('/speech-to-text', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 面试语音转写
  transcribeInterview(audio: File, questionIndex: number, interviewId: string, question?: string): Promise<BaseResponse<{ text: string }>> {
    // 验证文件格式
    const validFormats = ['audio/wav', 'audio/mpeg', 'audio/mp4', 'audio/x-m4a', 'audio/wma', 'audio/aac', 'audio/ogg', 'audio/amr', 'audio/flac'];
    if (!validFormats.includes(audio.type) && !validFormats.some(format => audio.name.endsWith(format.split('/')[1]))) {
      console.warn(`警告: 文件格式 ${audio.type} 可能不被支持，文件名: ${audio.name}`);
    }

    const formData = new FormData()
    formData.append('audio', audio)
    formData.append('questionIndex', questionIndex.toString())
    formData.append('interviewId', interviewId)

    // 如果有问题内容，也一起传递
    if (question) {
      formData.append('question', question)
    }

    return http.post('/speechToText/transcribe', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 获取面试转写结果
  getInterviewTranscriptions(interviewId: string) {
    return http.get(`/interview/transcriptions/${interviewId}`, {
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 上传面试视频
  uploadInterviewVideo(interviewId: string, videoFile: File): Promise<BaseResponse<{ videoUrl: string }>> {
    const formData = new FormData()
    formData.append('video', videoFile)
    return http.post(`/interviews/${interviewId}/video`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 300000  // 设置5分钟超时，视频文件较大
    })
  },

  // 更新面试视频URL（OSS直传后调用）
  updateInterviewVideoUrl(interviewId: string, videoUrl: string): Promise<BaseResponse<boolean>> {
    return http.post(`/interviews/${interviewId}/video-url`, { videoUrl })
  },

  // 分析面部表情
  analyzeFacialExpressions(video: Blob) {
    const formData = new FormData()
    formData.append('video', video)

    return http.post('/analyze/expressions', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 分析环境光线
  analyzeLighting(video: Blob) {
    const formData = new FormData()
    formData.append('video', video)

    return http.post('/analyze/lighting', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 分析环境噪音
  analyzeNoise(audio: Blob) {
    const formData = new FormData()
    formData.append('audio', audio)

    return http.post('/analyze/noise', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 120000  // 显式设置120秒超时
    })
  },

  // 更新面试状态
  updateInterviewStatus(interviewId: string): Promise<BaseResponse<boolean>> {
    return http.post(`/interviews/${interviewId}/status`)
  },

  // 获取面试详情
  getInterviewDetail(interviewId: string): Promise<{
    success: boolean;
    answer: Record<string, string>;
    questions: Record<string, string>;
    transcriptions: Record<string, string>;
    interview: {
      id: string;
      userId: number;
      type: string;
      candidateName: string;
      company: string | null;
      position: string;
      stage: string;
      experience: string;
      status: number;
      overallScore: number;
      feedback: string;
      strengths: string | null;
      improvements: string | null;
      videoUrl: string;
      interviewTime: string;
      createTime: string;
      updateTime: string;
    }
  }> {
    return http.get(`/interviews/${interviewId}`)
  }
} 