package com.bimowu.interview.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 简历服务接口
 */
public interface ResumeService {
    
    /**
     * 从简历文件中提取文本内容
     * @param file 简历文件(PDF, DOC, DOCX)
     * @return 提取的文本内容
     * @throws Exception 提取过程中可能的异常
     */
    String extractResumeText(MultipartFile file) throws Exception;
    
    /**
     * 根据简历文本生成面试问题
     * @param resumeText 简历文本内容
     * @return 生成的面试问题列表
     * @throws Exception 生成过程中可能的异常
     */
    List<String> generateInterviewQuestions(String resumeText) throws Exception;
    
    /**
     * 根据简历文本和面试类型生成针对性的面试问题
     * @param resumeText 简历文本内容
     * @param interviewStage 面试环节 (hr:HR面试, tech:技术面试)
     * @param interviewPosition 面试岗位 (dev:开发岗, support:技术支持岗)
     * @param interviewExperience 工作经验 (fresh:应届生, 1-3:1-3年, 3-5:3-5年, 5+:5年以上)
     * @return 生成的面试问题列表
     * @throws Exception 生成过程中可能的异常
     */
    List<String> generateInterviewQuestions(String resumeText, String interviewStage, String interviewPosition, String interviewExperience) throws Exception;
} 