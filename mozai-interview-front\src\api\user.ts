import http from '../utils/http';

// 用户相关API

/**
 * 获取用户信息
 * @returns 用户信息接口响应
 */
export const getUserInfo = async () => {
  try {
    return await http.get('/user/info');
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return {
      code: 500,
      message: '获取用户信息失败',
      data: null
    };
  }
};

/**
 * 退出登录
 * @returns 退出登录接口响应
 */
export const logout = async () => {
  try {
    return await http.post('/user/logout');
  } catch (error) {
    console.error('退出登录失败:', error);
    return {
      code: 500,
      message: '退出登录失败',
      data: null
    };
  }
};

/**
 * 账号密码登录
 * @param data 登录数据 {username, password, clientId}
 * @returns 登录结果
 */
export const loginByPassword = async (data: {username: string, password: string, clientId?: string}) => {
  try {
    return await http.post('/user/login/password', data);
  } catch (error) {
    console.error('账号密码登录失败:', error);
    return {
      code: 500,
      message: '账号密码登录失败',
      data: null
    };
  }
};

/**
 * 手机验证码登录
 * @param data 登录数据 {mobile, verifyCode, clientId}
 * @returns 登录结果
 */
export const loginByMobile = async (data: {mobile: string, verifyCode: string, clientId?: string}) => {
  try {
    return await http.post('/user/login/mobile', data);
  } catch (error) {
    console.error('手机验证码登录失败:', error);
    return {
      code: 500,
      message: '手机验证码登录失败',
      data: null
    };
  }
};

/**
 * 获取短信验证码
 * @param mobile 手机号
 * @returns 获取结果
 */
export const getVerifyCode = async (mobile: string) => {
  try {
    return await http.post('/user/verifyCode', { mobile });
  } catch (error) {
    console.error('获取验证码失败:', error);
    return {
      code: 500,
      message: '获取验证码失败',
      data: null
    };
  }
};

/**
 * 获取SSO登录地址
 * @returns 登录地址
 */
export const getLoginUrl = async () => {
  try {
    const response = await http.get('/user/loginUrl');
    return response;
  } catch (error) {
    console.error('获取登录地址失败:', error);
    return {
      code: 500,
      message: '获取登录地址失败',
      data: null
    };
  }
};

/**
 * 验证登录状态
 * @param token SSO token
 * @returns 验证结果
 */
export const validateToken = async (token: string) => {
  try {
    return await http.post('/user/validateToken', { token });
  } catch (error) {
    console.error('验证token失败:', error);
    return {
      code: 500,
      message: '验证token失败',
      data: null
    };
  }
};