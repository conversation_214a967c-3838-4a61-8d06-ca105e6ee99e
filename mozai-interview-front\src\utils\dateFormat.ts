/**
 * 日期格式化工具
 * 将ISO 8601格式的日期字符串 (如 2025-06-06T00:00:00.000+08:00)
 * 转换为指定格式 (如 yyyy-MM-dd HH:mm:ss)
 */

/**
 * 检查日期字符串是否包含错误格式
 * @param dateString 日期字符串
 * @returns 是否包含错误格式
 */
const hasInvalidFormat = (dateString: string): boolean => {
  if (!dateString) return false;
  
  // 检查是否包含明显的格式错误标记
  if (dateString.includes('yyyy') || 
      dateString.includes('Fr') || 
      dateString.includes('MM') || 
      dateString.includes('dd')) {
    return true;
  }
  
  // 尝试检查是否符合yyyy-MM-dd HH:mm:ss格式
  const regex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
  return !regex.test(dateString);
};

/**
 * 格式化日期为 yyyy-MM-dd HH:mm:ss 格式
 * @param dateString ISO格式的日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  
  // 如果包含明显的格式错误，使用当前时间
  if (hasInvalidFormat(dateString)) {
    console.warn('检测到无效的日期格式:', dateString);
    console.warn('使用当前时间替代');
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
  
  try {
    const date = new Date(dateString);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的日期字符串:', dateString);
      // 使用当前时间
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('格式化日期时出错:', error);
    // 返回当前时间
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
};

/**
 * 将日期格式化为ISO 8601格式 (用于API请求)
 * @param dateString 日期字符串或Date对象
 * @returns ISO格式的日期字符串
 */
export const toISOString = (dateString: string | Date): string => {
  if (!dateString) return '';
  
  // 如果是字符串且包含格式错误，使用当前时间
  if (typeof dateString === 'string' && hasInvalidFormat(dateString)) {
    console.warn('检测到无效的日期格式:', dateString);
    console.warn('使用当前时间替代');
    return new Date().toISOString();
  }
  
  try {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的日期:', dateString);
      return new Date().toISOString();
    }
    
    return date.toISOString();
  } catch (error) {
    console.error('转换为ISO日期时出错:', error);
    return new Date().toISOString();
  }
}; 