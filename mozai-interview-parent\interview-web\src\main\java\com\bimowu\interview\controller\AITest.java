package com.bimowu.interview.controller;

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChunk;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.Flowable;
import io.reactivex.schedulers.Schedulers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

//@RestController
//@RequestMapping("/api/ai")
@Slf4j
public class AITest {
    private final String apiKey = System.getenv("d0edfc49-557c-4c39-bdd3-1dc55fcb094a");
    private final ArkService service = ArkService
            .builder()
//            .baseUrl()
            .timeout(Duration.ofSeconds(120))
            .connectTimeout(Duration.ofSeconds(20))
            .retryTimes(2)
            .apiKey(apiKey)
            .build();

    @GetMapping("/test")
    public CompletableFuture<String> testAI() {
        log.info("开始AI测试请求");

        final List<ChatMessage> streamMessages = new ArrayList<>();
        final ChatMessage streamSystemMessage = ChatMessage.builder()
            .role(ChatMessageRole.SYSTEM)
            .content("你是豆包，是由字节跳动开发的 AI 人工智能助手")
            .build();
        final ChatMessage streamUserMessage = ChatMessage.builder()
            .role(ChatMessageRole.USER)
            .content("常见的十字花科植物有哪些？")
            .build();

        streamMessages.add(streamSystemMessage);
        streamMessages.add(streamUserMessage);

        ChatCompletionRequest streamChatCompletionRequest = ChatCompletionRequest.builder()
            .model("<Model>")
            .messages(streamMessages)
            .build();

        CompletableFuture<String> future = new CompletableFuture<>();
        StringBuilder responseBuilder = new StringBuilder();

        service.streamChatCompletion(streamChatCompletionRequest)
            .doOnError(error -> {
                log.error("AI请求失败", error);
                future.completeExceptionally(error);
            })
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.single())
            .subscribe(choice -> {
                if (choice.getChoices() != null && !choice.getChoices().isEmpty()) {
                    Object chunkChoice = choice.getChoices().get(0);
                    if (chunkChoice != null) {
                        try {
                            String content = chunkChoice.toString();
                            responseBuilder.append(content);
                            log.info("收到AI响应片段: {}", content);
                        } catch (Exception e) {
                            log.error("处理AI响应片段失败", e);
                        }
                    }
                }
            }, error -> {
                log.error("AI响应处理失败", error);
                future.completeExceptionally(error);
            }, () -> {
                String finalResponse = responseBuilder.toString();
                log.info("AI响应完成: {}", finalResponse);
                future.complete(finalResponse);
            });

        return future;
    }
}
