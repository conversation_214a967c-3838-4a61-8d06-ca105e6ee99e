import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '../store'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomeView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/sso-callback',
    name: 'SSOCallback',
    component: () => import('@/views/SSOCallbackView.vue'),
    // 路由元信息，标记为SSO回调路由
    meta: {
      isSSOCallback: true
    }
  },
  {
    path: '/selection',
    name: 'InterviewSelection',
    component: () => import('@/views/InterviewSelectionView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/upload',
    name: 'ResumeUpload',
    component: () => import('@/views/ResumeUploadView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/interview',
    name: 'Interview',
    component: () => import('@/views/InterviewView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/interviews',
    name: 'InterviewList',
    component: () => import('@/views/InterviewListView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/interviews/formal/upload',
    name: 'FormalInterviewUpload',
    component: () => import('@/views/FormalInterviewUploadView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/result',
    name: 'Result',
    component: () => import('@/views/ResultView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/end',
    name: 'End',
    component: () => import('@/views/EndView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 获取用户状态管理
  const userStore = useUserStore()

  // 检查URL中是否包含token参数（SSO登录成功后的重定向）
  const token = to.query.token as string | undefined
  
  // 尝试从localStorage获取token (可能在main.ts中已经设置)
  const localToken = localStorage.getItem('token')
  
  if (token) {
    console.log('检测到URL查询参数中的token:', token)
    // 将token保存到本地存储
    userStore.setToken(token)

    // 如果是专门的SSO回调路由，直接进入处理页面
    if (to.meta.isSSOCallback) {
      next()
      return
    }

    // 否则，移除URL中的token参数并重定向到同一页面
    // 这样可以防止token在浏览器历史记录中保留
    const query = { ...to.query }
    delete query.token
    next({
      path: to.path,
      query: query,
      replace: true // 替换当前历史记录，而不是添加新记录
    })
    return
  } else if (localToken && !userStore.isLoggedIn) {
    // 如果localStorage中有token但状态未更新，设置到store
    console.log('从localStorage中恢复token')
    userStore.setToken(localToken)
  }

  // 对于每个页面跳转都调用后端获取用户信息，触发SSO状态校验
  if (to.name !== 'SSOCallback') {
    try {
      // 不管有没有token，都调用后端获取用户信息
      console.log('路由守卫: 调用后端获取用户信息，触发SSO校验')
      await userStore.fetchUserInfo()
    } catch (error) {
      console.error('获取用户信息失败', error)
      // 即使获取失败也继续导航，由后端决定是否需要重定向
    }
  }

  // 对于其他所有路由，直接放行
  next()
})

export default router 