package com.bimowu.interview.controller;

import com.bimowu.interview.base.BaseResponse;
import com.bimowu.interview.service.TranscriptionService;
import com.bimowu.interview.service.InterviewTranscriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/speechToText")
@Api(tags = "语音转文字接口")
@Slf4j
public class SpeechToTextController {

    @Autowired
    private TranscriptionService transcriptionService;
    
    @Autowired
    private InterviewTranscriptionService interviewTranscriptionService;

    @PostMapping("/speech-to-text")
    @ApiOperation("将音频转换为文字")
    public BaseResponse<String> speechToText(
            @ApiParam(value = "音频文件", required = true) @RequestParam("audio") MultipartFile audioFile) {
        log.info("接收到语音转文字请求，文件大小：{}", audioFile.getSize());
        
        try {
            // 校验文件
            if (audioFile.isEmpty()) {
                return BaseResponse.error(400, "音频文件不能为空");
            }
            
            // 校验文件类型
            String originalFilename = audioFile.getOriginalFilename();
            String contentType = audioFile.getContentType();
            log.info("接收文件：{}，类型：{}", originalFilename, contentType);
            
            if (!isAudioFile(originalFilename)) {
                return BaseResponse.error(400, "不支持的文件格式，请上传WAV、MP3等音频文件");
            }
            
            // 调用语音识别服务
            String recognizedText = transcriptionService.speechToText(audioFile);
            return BaseResponse.ok(recognizedText);
        } catch (Exception e) {
            log.error("语音转文字失败", e);
            return BaseResponse.error(500, "语音转文字处理失败: " + e.getMessage());
        }
    }

    @PostMapping("/transcribe")
    @ApiOperation("处理面试录音并保存转写结果")
    public BaseResponse<String> transcribeInterview(
            @ApiParam(value = "音频文件", required = true) @RequestParam("audio") MultipartFile audioFile,
            @ApiParam(value = "问题索引", required = true) @RequestParam("questionIndex") Integer questionIndex,
            @ApiParam(value = "面试ID", required = true) @RequestParam("interviewId") String interviewId,
            @ApiParam(value = "问题内容", required = false) @RequestParam(value = "question", required = false) String question) {
        log.info("接收到面试问题{}的语音转写请求，面试ID: {}, 文件大小: {}", 
                  questionIndex, interviewId, audioFile.getSize());
        
        try {
            // 检查文件内容
            if (audioFile.isEmpty() || audioFile.getSize() == 0) {
                log.error("音频文件为空或大小为0");
                return BaseResponse.error(400, "音频文件不能为空或大小为0");
            }
            
            // 校验参数
            if (questionIndex == null || questionIndex < 0) {
                log.error("问题索引无效: {}", questionIndex);
                return BaseResponse.error(400, "问题索引无效");
            }
            
            if (interviewId == null || interviewId.isEmpty()) {
                log.error("面试ID不能为空");
                return BaseResponse.error(400, "面试ID不能为空");
            }
            
            // 检查文件类型是否支持
            String originalFilename = audioFile.getOriginalFilename();
            String contentType = audioFile.getContentType();
            log.info("接收文件：{}，类型：{}", originalFilename, contentType);
            
            // 检查文件扩展名
            boolean validExtension = isAudioFile(originalFilename);
            // 检查MIME类型
            boolean validMimeType = isSupportedMimeType(contentType);
            
            if (!validExtension && !validMimeType) {
                log.error("不支持的文件格式: 文件名={}, MIME类型={}", originalFilename, contentType);
                return BaseResponse.error(400, "不支持的文件格式，请上传WAV、MP3等音频文件");
            } else if (!validExtension) {
                // 仅文件扩展名不匹配，但MIME类型匹配，记录警告但继续处理
                log.warn("文件扩展名不符合要求，但MIME类型有效，尝试继续处理: {}", originalFilename);
            } else if (!validMimeType) {
                // 仅MIME类型不匹配，但文件扩展名匹配，记录警告但继续处理
                log.warn("MIME类型不符合要求，但文件扩展名有效，尝试继续处理: {}", contentType);
            }
            
            // 重写文件名确保包含有效扩展名
            String processedFilename = ensureValidAudioExtension(originalFilename);
            if (!processedFilename.equals(originalFilename)) {
                log.info("已修正文件名: {} -> {}", originalFilename, processedFilename);
            }
            
            // 关键修改：先读取文件内容到内存，避免异步处理时临时文件已被删除
            byte[] audioBytes;
            try {
                audioBytes = audioFile.getBytes();
                log.info("已读取音频文件到内存，大小：{} 字节", audioBytes.length);
            } catch (Exception e) {
                log.error("无法读取上传的文件", e);
                return BaseResponse.error(400, "无法读取上传的文件: " + e.getMessage());
            }
            
            // 创建一个新的MultipartFile实现，使用内存中的字节数组
            final MultipartFile inMemoryAudioFile = new InMemoryMultipartFile(
                    audioFile.getName(), 
                    processedFilename, 
                    contentType, 
                    audioBytes
            );
            
            // 异步处理语音转写，使用内存中的文件对象
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始异步处理问题{}的语音转写", questionIndex);
                    
                    // 调用interviewTranscriptionService保存转写结果到数据库
                    String result = interviewTranscriptionService.saveTranscription(
                            inMemoryAudioFile, questionIndex, interviewId, question);
                    
//                    // 同时保存到内存缓存中，用于兼容原有API
//                    transcriptionService.saveTranscription(inMemoryAudioFile, questionIndex, interviewId);
                    
                    log.info("面试问题{}的语音转写异步处理完成: {}", questionIndex, result);
                } catch (Exception e) {
                    log.error("面试问题{}的语音转写异步处理失败", questionIndex, e);
                }
            });
            
            // 立即返回接收成功的响应
            return BaseResponse.ok("语音转写请求已接收，正在后台处理");
        } catch (Exception e) {
            log.error("面试语音转写失败", e);
            return BaseResponse.error(500, "面试语音转写处理失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/transcriptions/{interviewId}")
    @ApiOperation("获取面试的所有转写结果")
    public BaseResponse<Map<Integer, String>> getInterviewTranscriptions(
            @ApiParam(value = "面试ID", required = true) @PathVariable("interviewId") String interviewId) {
        log.info("获取面试ID: {}的所有转写结果", interviewId);
        
        try {
            if (interviewId == null || interviewId.isEmpty()) {
                return BaseResponse.error(400, "面试ID不能为空");
            }
            
            Map<Integer, String> transcriptions = transcriptionService.getInterviewTranscriptions(interviewId);
            return BaseResponse.ok(transcriptions);
        } catch (Exception e) {
            log.error("获取面试转写结果失败", e);
            return BaseResponse.error(500, "获取面试转写结果失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否为支持的音频文件类型
     */
    private boolean isAudioFile(String filename) {
        if (filename == null || filename.isEmpty()) {
            log.warn("文件名为空");
            return false;
        }
        
        String lowerFilename = filename.toLowerCase();
        // 检查文件扩展名是否被支持
        boolean isSupported = lowerFilename.endsWith(".wav") || 
                lowerFilename.endsWith(".mp3") || 
                lowerFilename.endsWith(".mp4") || 
                lowerFilename.endsWith(".m4a") || 
                lowerFilename.endsWith(".aac") || 
                lowerFilename.endsWith(".flac") || 
                lowerFilename.endsWith(".ogg") || 
                lowerFilename.endsWith(".wma") || 
                lowerFilename.endsWith(".amr") || 
                lowerFilename.endsWith(".webm");
        
        if (!isSupported) {
            log.warn("不支持的音频文件格式: {}", lowerFilename);
        }
        
        return isSupported;
    }
    
    /**
     * 检查MIME类型是否为支持的音频类型
     */
    private boolean isSupportedMimeType(String contentType) {
        if (contentType == null || contentType.isEmpty()) {
            log.warn("MIME类型为空");
            return false;
        }
        
        // 检查MIME类型是否被支持
        boolean isSupported = contentType.startsWith("audio/") || 
                contentType.equals("application/octet-stream"); // 有时浏览器会发送这种通用类型
        
        if (!isSupported) {
            log.warn("不支持的MIME类型: {}", contentType);
        }
        
        return isSupported;
    }

    /**
     * 确保文件名有有效的音频扩展名
     * 如果原文件名没有有效扩展名，根据MIME类型添加一个
     */
    private String ensureValidAudioExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "audio.wav"; // 默认文件名
        }
        
        // 检查是否已有有效扩展名
        String lowerFilename = filename.toLowerCase();
        if (lowerFilename.endsWith(".wav") || 
            lowerFilename.endsWith(".mp3") || 
            lowerFilename.endsWith(".mp4") || 
            lowerFilename.endsWith(".m4a") || 
            lowerFilename.endsWith(".aac") || 
            lowerFilename.endsWith(".flac") || 
            lowerFilename.endsWith(".ogg") || 
            lowerFilename.endsWith(".wma") || 
            lowerFilename.endsWith(".amr") || 
            lowerFilename.endsWith(".webm")) {
            return filename; // 已有有效扩展名
        }
        
        // 没有有效扩展名，添加.wav
        return filename + ".wav";
    }

    /**
     * 内存中的MultipartFile实现，避免临时文件访问问题
     */
    @Data
    private static class InMemoryMultipartFile implements MultipartFile {
        private final String name;
        private final String originalFilename;
        private final String contentType;
        private final byte[] content;

        public InMemoryMultipartFile(String name, String originalFilename, String contentType, byte[] content) {
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.content = content;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fileOutputStream = new FileOutputStream(dest)) {
                fileOutputStream.write(content);
            }
        }
    }
} 