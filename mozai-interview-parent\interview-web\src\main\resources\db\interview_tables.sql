

DROP TABLE IF EXISTS `interview`;
CREATE TABLE `interview` (
  `id` varchar(64) NOT NULL COMMENT '面试ID',
  `user_id` int NOT NULL COMMENT '用户id',
  `type` varchar(20) NOT NULL DEFAULT 'mock' COMMENT '面试类型(mock:模拟面试,formal:正式面试)',
  `candidate_name` varchar(50) DEFAULT NULL COMMENT '应聘者姓名',
  `company` varchar(100) DEFAULT NULL COMMENT '面试公司(正式面试)',
  `position` varchar(50) DEFAULT NULL COMMENT '面试职位',
  `stage` varchar(20) DEFAULT NULL COMMENT '面试阶段(hr/tech)',
  `experience` varchar(20) DEFAULT NULL COMMENT '工作经验(fresh/1-3/3-5/5+)',
  `status` tinyint DEFAULT '0' COMMENT '面试状态(0:进行中,1:等待结果,2:已完成)',
  `result` tinyint DEFAULT NULL COMMENT '面试结果(0:未通过,1:通过,NULL:未评定)',
  `overall_score` int DEFAULT NULL COMMENT '总体评分(0-100)',
  `feedback` varchar(200) DEFAULT NULL COMMENT '面试反馈',
  `strengths` varchar(200) DEFAULT NULL COMMENT '优势表现',
  `improvements` varchar(200) DEFAULT NULL COMMENT '改进建议',
  `video_url` varchar(255) DEFAULT NULL COMMENT '面试视频文件URL',
  `interview_time` datetime DEFAULT NULL COMMENT '面试时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_stage_position` (`stage`,`position`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_interview_time` (`interview_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试信息表';



DROP TABLE IF EXISTS `interview_questions`;
CREATE TABLE `interview_questions` (
  `interview_id` varchar(64) NOT NULL COMMENT '面试ID',
  `questions` varchar(10000) DEFAULT NULL COMMENT '问题列表',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`interview_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试问题表';


DROP TABLE IF EXISTS `interview_speech_chapter`;
CREATE TABLE `interview_speech_chapter` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `interview_id` varchar(64) NOT NULL COMMENT '面试ID',
  `start_time` int NOT NULL COMMENT '开始时间(秒)',
  `end_time` int NOT NULL COMMENT '结束时间(秒)',
  `chapter_title` varchar(255) NOT NULL COMMENT '章节标题',
  `chapter_summary` varchar(1000) DEFAULT NULL COMMENT '章节总结',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_interview_id` (`interview_id`),
  KEY `idx_time_range` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试语音转文字章节段落记录表';



DROP TABLE IF EXISTS `interview_speech_record`;
CREATE TABLE `interview_speech_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `interview_id` varchar(64) NOT NULL COMMENT '面试ID',
  `speaker_id` int NOT NULL DEFAULT '0' COMMENT '发言人ID',
  `start_time` int NOT NULL COMMENT '开始时间(秒)',
  `end_time` int NOT NULL COMMENT '结束时间(秒)',
  `content` text COMMENT '发言内容',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_interview_id` (`interview_id`),
  KEY `idx_speaker_id` (`speaker_id`),
  KEY `idx_time_range` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试语音转文字记录表';



DROP TABLE IF EXISTS `interview_transcription`;
CREATE TABLE `interview_transcription` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `interview_id` varchar(64) NOT NULL COMMENT '面试ID',
  `question_index` int NOT NULL COMMENT '问题索引',
  `question` text COMMENT '问题内容',
  `transcription` text COMMENT '转写的回答内容',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频文件URL',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_interview_question` (`interview_id`,`question_index`),
  KEY `idx_interview_id` (`interview_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1929748171097956355 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='面试转写记录表';
