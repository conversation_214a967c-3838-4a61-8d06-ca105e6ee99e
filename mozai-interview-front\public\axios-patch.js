/**
 * Axios补丁 - 在axios实例加载后应用
 * 此脚本会在页面加载时运行，强制修改axios默认设置
 */
console.log('【Axios补丁】等待axios加载...');

// 检查axios是否已加载
function checkAxios() {
  if (window.axios) {
    patchAxios();
  } else {
    setTimeout(checkAxios, 100);
  }
}

// 修补axios
function patchAxios() {
  console.log('【Axios补丁】发现axios，应用补丁');
  
  // 保存原始的axios create方法
  const originalCreate = window.axios.create;
  
  // 修改默认配置
  window.axios.defaults.timeout = 120000;
  console.log('【Axios补丁】设置axios.defaults.timeout =', window.axios.defaults.timeout);
  
  // 重写create方法
  window.axios.create = function(config) {
    config = config || {};
    config.timeout = 120000; // 强制设置超时为120秒
    console.log('【Axios补丁】创建axios实例，设置timeout =', config.timeout);
    return originalCreate.call(this, config);
  };
  
  console.log('【Axios补丁】axios补丁应用完成');
}

// 开始检查
checkAxios();

// 如果在页面上找到了Vue实例，直接设置一些全局变量
if (window.Vue) {
  console.log('【Axios补丁】找到Vue实例，设置全局变量');
  window.FORCE_TIMEOUT = 120000;
}

// 告知用户补丁已加载
console.log('【Axios补丁】补丁脚本已加载'); 