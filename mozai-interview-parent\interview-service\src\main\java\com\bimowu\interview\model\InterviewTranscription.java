package com.bimowu.interview.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 面试转写记录实体类
 */
@Getter
@Setter
@Slf4j
public class InterviewTranscription {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 面试ID
     */
    private String interviewId;
    
    /**
     * 问题索引
     */
    private Integer questionIndex;
    
    /**
     * 问题内容
     */
    private String question;
    
    /**
     * 转写的回答内容
     */
    private String transcription;
    
    /**
     * 音频文件URL
     */
    private String audioUrl;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 