package com.bimowu.interview.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.interview.model.Interview;

import java.util.List;
import java.util.Map;

/**
 * 面试服务接口
 */
public interface InterviewService extends IService<Interview> {
    
    /**
     * 创建面试
     * 
     * @param interview 面试信息
     * @return 面试ID
     */
    String createInterview(Interview interview);
    
    /**
     * 更新面试信息
     * 
     * @param interview 面试信息
     * @return 是否成功
     */
    boolean updateInterview(Interview interview);
    
    /**
     * 获取面试信息
     * 
     * @param interviewId 面试ID
     * @return 面试信息
     */
    Interview getInterviewById(String interviewId);
    
    /**
     * 根据条件查询面试列表
     * 
     * @param params 查询条件
     * @return 面试列表
     */
    List<Interview> getInterviewsByCondition(Map<String, Object> params);
    
    /**
     * 根据类型获取面试列表
     * 
     * @param type 面试类型
     * @return 面试列表
     */
    List<Interview> getInterviewsByType(String type);
    
    /**
     * 更新面试状态
     * 
     * @param interviewId 面试ID
     * @param status 面试状态
     * @return 是否成功
     */
    boolean updateInterviewStatus(String interviewId, Integer status);
    
    /**
     * 更新面试结果
     * 
     * @param interviewId 面试ID
     * @param overallScore 总体评分
     * @param feedback 面试反馈
     * @param strengths 优势表现
     * @param improvements 改进建议
     * @return 是否成功
     */
    boolean updateInterviewResult(String interviewId, Integer overallScore, String feedback, 
                                String strengths, String improvements);
    
    /**
     * 更新面试通过/未通过状态
     * 
     * @param interviewId 面试ID
     * @param result 面试结果(0:未通过,1:通过)
     * @return 是否成功
     */
    boolean updateInterviewPassResult(String interviewId, Integer result);
    
    /**
     * 更新面试视频URL
     * 
     * @param interviewId 面试ID
     * @param videoUrl 视频URL
     * @return 是否成功
     */
    boolean updateInterviewVideoUrl(String interviewId, String videoUrl);
    
    /**
     * 删除面试
     * 
     * @param interviewId 面试ID
     * @return 是否成功
     */
    boolean deleteInterview(String interviewId);
    
    /**
     * 保存面试问题列表
     * 
     * @param interviewId 面试ID
     * @param questions 问题列表
     * @return 是否成功
     */
    boolean saveInterviewQuestions(String interviewId, List<String> questions);
    
    /**
     * 获取面试问题列表
     * 
     * @param interviewId 面试ID
     * @return 问题列表
     */
    List<String> getInterviewQuestions(String interviewId);

    /**
     * 保存对话转写记录
     *
     * @param dialogList 对话记录列表
     * @param interviewId 面试ID
     * @return 是否保存成功
     */
    boolean saveDialogRecords(List<Object> dialogList, String interviewId);

    /**
     * 保存章节转写记录
     *
     * @param chapterList 章节记录列表
     * @param interviewId 面试ID
     * @return 是否保存成功
     */
    boolean saveChapterRecords(List<Object> chapterList, String interviewId);
    
    /**
     * 保存问答转写记录
     *
     * @param qaList 问答记录列表
     * @param interviewId 面试ID
     * @return 是否保存成功
     */
    boolean saveTranscriptionRecords(List<Object> qaList, String interviewId);
} 