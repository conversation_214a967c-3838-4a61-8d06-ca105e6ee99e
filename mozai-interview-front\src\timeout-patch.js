// 超时补丁 - 强制所有XMLHttpRequest设置120秒超时
(function() {
  console.log('应用XMLHttpRequest超时补丁');
  
  // 保存原始的XMLHttpRequest构造函数
  const originalXhr = window.XMLHttpRequest;
  
  // 创建新的构造函数
  function CustomXMLHttpRequest() {
    const xhr = new originalXhr();
    
    // 设置超时为120秒
    xhr.timeout = 120000;
    
    // 记录超时设置
    console.log('已创建XMLHttpRequest，设置超时为:', xhr.timeout);
    
    return xhr;
  }
  
  // 复制原型链
  CustomXMLHttpRequest.prototype = originalXhr.prototype;
  
  // 替换全局XMLHttpRequest
  window.XMLHttpRequest = CustomXMLHttpRequest;
  
  console.log('XMLHttpRequest超时补丁已应用');
})(); 