{"female": {"type": "gltf", "version": "2.0", "asset": {"version": "2.0", "generator": "Quiz AI Interview"}, "scene": 0, "scenes": [{"nodes": [0, 3, 4]}], "nodes": [{"name": "body", "mesh": 0, "translation": [0, 0.8, 0]}, {"name": "head", "mesh": 1, "translation": [0, 1.6, 0]}, {"name": "eyes", "mesh": 2, "translation": [0, 1.7, 0.3]}, {"name": "ambient_light", "extensions": {"KHR_lights_punctual": {"light": 0}}}, {"name": "directional_light", "rotation": [-0.3, 0.4, 0.3, 0.9], "extensions": {"KHR_lights_punctual": {"light": 1}}}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2}, "indices": 0, "material": 0}]}, {"primitives": [{"attributes": {"POSITION": 3, "NORMAL": 4}, "indices": 0, "material": 1}]}, {"primitives": [{"attributes": {"POSITION": 5, "NORMAL": 6}, "indices": 0, "material": 2}]}], "materials": [{"name": "body_material", "pbrMetallicRoughness": {"baseColorFactor": [0.5, 0.5, 0.8, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.8}}, {"name": "head_material", "pbrMetallicRoughness": {"baseColorFactor": [0.9, 0.8, 0.7, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.7}}, {"name": "eye_material", "pbrMetallicRoughness": {"baseColorFactor": [0.1, 0.1, 0.1, 1.0], "metallicFactor": 0.5, "roughnessFactor": 0.2}}], "extensions": {"KHR_lights_punctual": {"lights": [{"name": "ambient", "color": [1.0, 1.0, 1.0], "intensity": 0.5, "type": "ambient"}, {"name": "directional", "color": [1.0, 1.0, 1.0], "intensity": 1.0, "type": "directional"}]}}, "extensionsUsed": ["KHR_lights_punctual"]}, "male": {"type": "gltf", "version": "2.0", "asset": {"version": "2.0", "generator": "Quiz AI Interview"}, "scene": 0, "scenes": [{"nodes": [0, 3, 4]}], "nodes": [{"name": "body", "mesh": 0, "translation": [0, 0.9, 0], "scale": [1.1, 1.1, 1.1]}, {"name": "head", "mesh": 1, "translation": [0, 1.8, 0], "scale": [1.1, 1.1, 1.1]}, {"name": "eyes", "mesh": 2, "translation": [0, 1.9, 0.3], "scale": [1.1, 1.1, 1.1]}, {"name": "ambient_light", "extensions": {"KHR_lights_punctual": {"light": 0}}}, {"name": "directional_light", "rotation": [-0.3, 0.4, 0.3, 0.9], "extensions": {"KHR_lights_punctual": {"light": 1}}}], "meshes": [{"primitives": [{"attributes": {"POSITION": 1, "NORMAL": 2}, "indices": 0, "material": 0}]}, {"primitives": [{"attributes": {"POSITION": 3, "NORMAL": 4}, "indices": 0, "material": 1}]}, {"primitives": [{"attributes": {"POSITION": 5, "NORMAL": 6}, "indices": 0, "material": 2}]}], "materials": [{"name": "body_material", "pbrMetallicRoughness": {"baseColorFactor": [0.3, 0.4, 0.7, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.8}}, {"name": "head_material", "pbrMetallicRoughness": {"baseColorFactor": [0.8, 0.7, 0.6, 1.0], "metallicFactor": 0.0, "roughnessFactor": 0.7}}, {"name": "eye_material", "pbrMetallicRoughness": {"baseColorFactor": [0.1, 0.1, 0.1, 1.0], "metallicFactor": 0.5, "roughnessFactor": 0.2}}], "extensions": {"KHR_lights_punctual": {"lights": [{"name": "ambient", "color": [1.0, 1.0, 1.0], "intensity": 0.5, "type": "ambient"}, {"name": "directional", "color": [1.0, 1.0, 1.0], "intensity": 1.0, "type": "directional"}]}}, "extensionsUsed": ["KHR_lights_punctual"]}}