package com.bimowu.interview.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.interview.model.ResumeProgress;
import org.mapstruct.Mapper;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * 简历进度管理Mapper接口
 */
@Mapper
public interface ResumeProgressMapper extends BaseMapper<ResumeProgress> {

    /**
     * 插入进度信息
     * 
     * @param resumeProgress 进度信息
     * @return 影响的行数
     */
    int insert(ResumeProgress resumeProgress);
    
    /**
     * 更新进度信息
     * 
     * @param resumeProgress 进度信息
     * @return 影响的行数
     */
    int update(ResumeProgress resumeProgress);
    
    /**
     * 根据ID查询进度信息
     * 
     * @param id 进度ID
     * @return 进度信息
     */
    ResumeProgress selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID查询进度信息
     * 
     * @param userId 用户ID
     * @return 进度信息
     */
    ResumeProgress selectByUserId(@Param("userId") Integer userId);
    
    /**
     * 根据简历ID查询进度信息
     * 
     * @param resumeId 简历ID
     * @return 进度信息
     */
    ResumeProgress selectByResumeId(@Param("resumeId") Long resumeId);
    
    /**
     * 根据条件查询进度信息列表
     * 
     * @param params 查询条件
     * @return 进度信息列表
     */
    List<ResumeProgress> selectByCondition(Map<String, Object> params);
    
    /**
     * 更新进度阶段
     * 
     * @param id 进度ID
     * @param currentStage 当前阶段
     * @return 影响的行数
     */
    int updateStage(@Param("id") Long id, @Param("currentStage") String currentStage);
    
    /**
     * 逻辑删除进度信息
     * 
     * @param id 进度ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);
} 