package com.bimowu.interview.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.interview.dao.InterviewSpeechChapterMapper;
import com.bimowu.interview.model.InterviewSpeechChapter;
import com.bimowu.interview.service.InterviewSpeechChapterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 面试语音转文字章节段落记录服务实现类
 */
@Service
@Slf4j
public class InterviewSpeechChapterServiceImpl extends ServiceImpl<InterviewSpeechChapterMapper, InterviewSpeechChapter>
        implements InterviewSpeechChapterService {

    @Autowired
    private InterviewSpeechChapterMapper interviewSpeechChapterMapper;

    @Override
    @Transactional
    public Long saveChapter(InterviewSpeechChapter chapter) {
        log.info("保存章节记录: interviewId={}, chapterTitle={}, startTime={}, endTime={}",
                chapter.getInterviewId(), chapter.getChapterTitle(), chapter.getStartTime(), chapter.getEndTime());
        
        // 设置时间
        Date now = new Date();
        chapter.setCreateTime(now);
        chapter.setUpdateTime(now);
        
        interviewSpeechChapterMapper.insert(chapter);
        return chapter.getId();
    }

    @Override
    @Transactional
    public boolean batchSaveChapters(List<InterviewSpeechChapter> chapters) {
        log.info("批量保存章节记录: count={}", chapters.size());
        
        if (chapters == null || chapters.isEmpty()) {
            log.warn("章节记录列表为空，没有数据需要保存");
            return false;
        }
        
        try {
            // 设置时间，如果没有的话
            Date now = new Date();
            for (InterviewSpeechChapter chapter : chapters) {
                if (chapter.getCreateTime() == null) {
                    chapter.setCreateTime(now);
                }
                if (chapter.getUpdateTime() == null) {
                    chapter.setUpdateTime(now);
                }
            }
            
            // 使用MyBatis-Plus的批量插入方法
            boolean result = this.saveBatch(chapters);
            log.info("批量保存章节记录完成: {}", result ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            log.error("批量保存章节记录失败", e);
            throw new RuntimeException("批量保存章节记录失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean updateChapter(InterviewSpeechChapter chapter) {
        log.info("更新章节记录: id={}", chapter.getId());
        chapter.setUpdateTime(new Date());
        
        int result = interviewSpeechChapterMapper.update(chapter);
        return result > 0;
    }

    @Override
    public InterviewSpeechChapter getChapterById(Long id) {
        log.info("获取章节记录: id={}", id);
        return interviewSpeechChapterMapper.selectById(id);
    }

    @Override
    public List<InterviewSpeechChapter> getChaptersByInterviewId(String interviewId) {
        log.info("根据面试ID获取章节记录列表: interviewId={}", interviewId);
        return interviewSpeechChapterMapper.selectByInterviewId(interviewId);
    }

    @Override
    public List<InterviewSpeechChapter> getChaptersByTimeRange(String interviewId, Integer startTime, Integer endTime) {
        log.info("根据时间范围获取章节记录列表: interviewId={}, startTime={}, endTime={}",
                interviewId, startTime, endTime);
        return interviewSpeechChapterMapper.selectByTimeRange(interviewId, startTime, endTime);
    }

    @Override
    public List<InterviewSpeechChapter> getChaptersByCondition(Map<String, Object> params) {
        log.info("根据条件获取章节记录列表: params={}", params);
        return interviewSpeechChapterMapper.selectByCondition(params);
    }

    @Override
    @Transactional
    public boolean deleteChapter(Long id) {
        log.info("删除章节记录: id={}", id);
        int result = interviewSpeechChapterMapper.deleteById(id);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteChaptersByInterviewId(String interviewId) {
        log.info("删除面试的所有章节记录: interviewId={}", interviewId);
        int result = interviewSpeechChapterMapper.deleteByInterviewId(interviewId);
        return result > 0;
    }
} 