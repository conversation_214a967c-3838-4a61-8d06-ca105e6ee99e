<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bimowu.interview.dao.InterviewSpeechChapterMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.InterviewSpeechChapter">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="interview_id" property="interviewId" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="INTEGER"/>
        <result column="end_time" property="endTime" jdbcType="INTEGER"/>
        <result column="chapter_title" property="chapterTitle" jdbcType="VARCHAR"/>
        <result column="chapter_summary" property="chapterSummary" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, interview_id, start_time, end_time, chapter_title, chapter_summary, create_time, update_time
    </sql>
    
    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.bimowu.interview.model.InterviewSpeechChapter" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO interview_speech_chapter (
            interview_id, start_time, end_time, chapter_title, chapter_summary, create_time, update_time
        ) VALUES (
            #{interviewId,jdbcType=VARCHAR},
            #{startTime,jdbcType=INTEGER},
            #{endTime,jdbcType=INTEGER},
            #{chapterTitle,jdbcType=VARCHAR},
            #{chapterSummary,jdbcType=VARCHAR},
            NOW(),
            NOW()
        )
    </insert>
    
    <!-- 更新记录 -->
    <update id="update" parameterType="com.bimowu.interview.model.InterviewSpeechChapter">
        UPDATE interview_speech_chapter
        <set>
            <if test="interviewId != null">
                interview_id = #{interviewId,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=INTEGER},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=INTEGER},
            </if>
            <if test="chapterTitle != null">
                chapter_title = #{chapterTitle,jdbcType=VARCHAR},
            </if>
            <if test="chapterSummary != null">
                chapter_summary = #{chapterSummary,jdbcType=VARCHAR},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>
    
    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_chapter
        WHERE id = #{id,jdbcType=BIGINT}
    </select>
    
    <!-- 根据面试ID查询记录 -->
    <select id="selectByInterviewId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_chapter
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
        ORDER BY start_time ASC
    </select>
    
    <!-- 根据面试ID和时间范围查询记录 -->
    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_chapter
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
        AND start_time >= #{startTime,jdbcType=INTEGER}
        AND end_time &lt;= #{endTime,jdbcType=INTEGER}
        ORDER BY start_time ASC
    </select>
    
    <!-- 根据条件查询记录 -->
    <select id="selectByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_speech_chapter
        <where>
            <if test="interviewId != null">
                AND interview_id = #{interviewId,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                AND start_time >= #{startTime,jdbcType=INTEGER}
            </if>
            <if test="endTime != null">
                AND end_time &lt;= #{endTime,jdbcType=INTEGER}
            </if>
            <if test="chapterTitle != null">
                AND chapter_title LIKE CONCAT('%', #{chapterTitle,jdbcType=VARCHAR}, '%')
            </if>
            <if test="chapterSummary != null">
                AND chapter_summary LIKE CONCAT('%', #{chapterSummary,jdbcType=VARCHAR}, '%')
            </if>
            <if test="createTimeStart != null">
                AND create_time >= #{createTimeStart,jdbcType=TIMESTAMP}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY start_time ASC
    </select>
    
    <!-- 根据ID删除记录 -->
    <delete id="deleteById">
        DELETE FROM interview_speech_chapter
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>
    
    <!-- 根据面试ID删除所有记录 -->
    <delete id="deleteByInterviewId">
        DELETE FROM interview_speech_chapter
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
    </delete>
</mapper> 