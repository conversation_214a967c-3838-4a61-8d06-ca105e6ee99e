package com.bimowu.interview.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.interview.model.InterviewSpeechRecord;

import java.util.List;
import java.util.Map;

/**
 * 面试语音转文字记录服务接口
 */
public interface InterviewSpeechRecordService extends IService<InterviewSpeechRecord> {
    
    /**
     * 保存语音记录
     * 
     * @param record 语音记录
     * @return 记录ID
     */
    Long saveRecord(InterviewSpeechRecord record);
    
    /**
     * 批量保存语音记录
     * 
     * @param records 语音记录列表
     * @return 是否成功
     */
    boolean batchSaveRecords(List<InterviewSpeechRecord> records);
    
    /**
     * 更新语音记录
     * 
     * @param record 语音记录
     * @return 是否成功
     */
    boolean updateRecord(InterviewSpeechRecord record);
    
    /**
     * 获取语音记录
     * 
     * @param id 记录ID
     * @return 语音记录
     */
    InterviewSpeechRecord getRecordById(Long id);
    
    /**
     * 根据面试ID获取语音记录列表
     * 
     * @param interviewId 面试ID
     * @return 语音记录列表
     */
    List<InterviewSpeechRecord> getRecordsByInterviewId(String interviewId);
    
    /**
     * 根据面试ID和时间范围获取语音记录
     * 
     * @param interviewId 面试ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 语音记录列表
     */
    List<InterviewSpeechRecord> getRecordsByTimeRange(String interviewId, Integer startTime, Integer endTime);
    
    /**
     * 根据面试ID和发言人ID获取语音记录
     * 
     * @param interviewId 面试ID
     * @param speakerId 发言人ID
     * @return 语音记录列表
     */
    List<InterviewSpeechRecord> getRecordsBySpeaker(String interviewId, Integer speakerId);
    
    /**
     * 根据条件查询语音记录列表
     * 
     * @param params 查询条件
     * @return 语音记录列表
     */
    List<InterviewSpeechRecord> getRecordsByCondition(Map<String, Object> params);
    
    /**
     * 删除语音记录
     * 
     * @param id 记录ID
     * @return 是否成功
     */
    boolean deleteRecord(Long id);
    
    /**
     * 删除面试的所有语音记录
     * 
     * @param interviewId 面试ID
     * @return 是否成功
     */
    boolean deleteRecordsByInterviewId(String interviewId);
} 