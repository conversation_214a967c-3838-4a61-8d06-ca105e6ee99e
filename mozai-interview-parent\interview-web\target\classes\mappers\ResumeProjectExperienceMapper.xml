<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.ResumeProjectExperienceMapper">

    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.ResumeProjectExperience">
            <id property="expId" column="exp_id" jdbcType="BIGINT"/>
            <result property="resumeId" column="resume_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="timePeriod" column="time_period" jdbcType="VARCHAR"/>
            <result property="positionType" column="position_type" jdbcType="VARCHAR"/>
            <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
            <result property="role" column="role" jdbcType="VARCHAR"/>
            <result property="projectDescription" column="project_description" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="createAt" column="create_at" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        exp_id,resume_id,project_id,
        time_period,position_type,project_name,
        role,project_description,create_time,
        update_time,create_at,is_delete
    </sql>
</mapper>
