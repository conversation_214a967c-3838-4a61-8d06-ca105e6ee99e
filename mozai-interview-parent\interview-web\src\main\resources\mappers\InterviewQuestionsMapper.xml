<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.InterviewQuestionsMapper">

    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.InterviewQuestions">
            <id property="interviewId" column="interview_id" jdbcType="VARCHAR"/>
            <result property="questions" column="questions" jdbcType="OTHER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        interview_id,questions,create_time,
        update_time
    </sql>
</mapper>
