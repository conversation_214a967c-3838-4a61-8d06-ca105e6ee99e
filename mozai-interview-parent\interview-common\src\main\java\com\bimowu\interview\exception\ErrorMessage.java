package com.bimowu.interview.exception;

/**
 * <AUTHOR>
 * @date 2019-03-11 15:46
 */
public enum ErrorMessage {

    /**
     * 通用ERROR
     */
    SYS_ERROR(10000, "系统开小差了,稍后再试"), PARAM_ERROR(10001, "参数错误"), LOGIN_ERROR(10002, "用户未登录"),

    /**
     * 前台ERROR
     */
    ASSESS_NOT_FOUND_SET(20001, "无法找到有效的题集信息！"), QUESTION_SET_NOT_FOUND(20002, "无法找到有效的题集关系！"),
    USER_ANSWER_NOT_FOUND(20003, "用户答题信息不存在！"), ANSWER_STATUS_NOT_ALLOWED_COMMIT(20004, "用户答题状态不允许提交"),
    ANSWER_STATUS_ERROR(20005, "用户答题状态异常"), NOT_NEED_CORRECT(20006, "状态不需要判卷"),

    CORRECT_TYPE_ERROR(20007, "判卷类型异常"), ANSWER_STATUS_SET_ERROR(20008, "设置用户题目状态异常"),

    ADMIN_LOGIN_ERROR(30001, "管理员未登录"), ADMIN_LOGIN_NAME_PWD_ERROR(30002, "用户名密码错误");

    private Integer code;
    private String message;

    ErrorMessage(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public ErrorMessage setMessage(String message) {
        this.message = message;
        return this;
    }
}
