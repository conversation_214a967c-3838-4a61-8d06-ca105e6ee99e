package com.bimowu.interview.model;


import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 职位类别表
 * @TableName resume_category
 */
@TableName(value ="resume_category")
@Data
public class ResumeCategory implements Serializable {
    /**
     * 主键ID
     */
    @TableId
    private Long catId;

    /**
     * 职位类别名称
     */
    private String name;

    /**
     * 职位类别编码
     */
    private String code;

    /**
     * 职位类别描述
     */
    private String description;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态(0-禁用,1-启用)
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建人
     */
    private String createAt;

    /**
     * 删除标识(0-未删除,1-已删除)
     */
    @TableLogic
    @TableField(select = false)
    private Integer isDelete;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}