#!/bin/bash

echo "🧪 测试 Node.js 16 构建修复"
echo "Node.js 版本: $(node --version)"
echo "NPM 版本: $(npm --version)"

# 检查关键文件是否存在
echo "📋 检查配置文件..."
if [ ! -f "vite.config.node16.ts" ]; then
    echo "❌ vite.config.node16.ts 不存在"
    exit 1
fi

if [ ! -f "scripts/build-simple.js" ]; then
    echo "❌ scripts/build-simple.js 不存在"
    exit 1
fi

echo "✅ 配置文件检查通过"

# 检查依赖版本
echo "📦 检查关键依赖版本..."
VITE_VERSION=$(npm list vite --depth=0 2>/dev/null | grep vite@ | sed 's/.*vite@//' | sed 's/ .*//')
ELEMENT_VERSION=$(npm list element-plus --depth=0 2>/dev/null | grep element-plus@ | sed 's/.*element-plus@//' | sed 's/ .*//')

echo "Vite 版本: $VITE_VERSION"
echo "Element Plus 版本: $ELEMENT_VERSION"

# 尝试构建
echo "🏗️ 开始测试构建..."
npm run build:simple

if [ $? -eq 0 ]; then
    echo "✅ 构建测试成功！"
    
    # 检查构建产物
    if [ -d "dist-interview-ai" ]; then
        echo "✅ 构建目录已生成"
        echo "📁 构建产物大小:"
        du -sh dist-interview-ai
    else
        echo "❌ 构建目录未生成"
        exit 1
    fi
else
    echo "❌ 构建测试失败"
    exit 1
fi

echo "🎉 所有测试通过！"
