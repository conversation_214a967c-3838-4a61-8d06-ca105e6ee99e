import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// Node.js 16 专用配置
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    },
    // 强制使用 CommonJS 版本的 Element Plus
    conditions: ['import', 'module', 'browser', 'default']
  },
  base: './',
  define: {
    // Node.js 16 兼容性
    global: 'globalThis',
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false
  },
  build: {
    outDir: 'dist-interview-ai',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    // 简化构建配置以避免兼容性问题
    rollupOptions: {
      output: {
        manualChunks: {
          'element-plus': ['element-plus'],
          'vue-vendor': ['vue', 'vue-router', 'pinia']
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      },
      // 外部化可能有问题的依赖
      external: process.env.SKIP_OPTIONAL_DEPENDENCIES === 'true' ? ['ali-oss'] : []
    },
    // 减少构建警告
    chunkSizeWarningLimit: 1000,
    // 兼容性设置
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  server: {
    port: 3000,
    open: true,
    cors: true
  },
  // Element Plus 优化
  optimizeDeps: {
    include: [
      'element-plus/es/components/button/style/css',
      'element-plus/es/components/input/style/css',
      'element-plus/es/components/form/style/css',
      'element-plus/es/components/form-item/style/css',
      'element-plus/es/components/message/style/css',
      'element-plus/es/components/card/style/css',
      'element-plus/es/components/tag/style/css',
      'element-plus/es/components/progress/style/css',
      'element-plus/es/components/collapse/style/css',
      'element-plus/es/components/collapse-item/style/css',
      'element-plus/es/components/skeleton/style/css',
      'element-plus/es/components/icon/style/css',
      'element-plus/es/components/rate/style/css',
      'element-plus/es/components/upload/style/css'
    ],
    exclude: ['ali-oss']
  }
})
