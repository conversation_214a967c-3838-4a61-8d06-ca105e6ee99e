#!/bin/bash

echo "🚀 Element Plus 终极修复方案"
echo "Node.js 版本: $(node --version)"

# 函数：清理环境
cleanup() {
    echo "🧹 清理构建环境..."
    rm -rf node_modules package-lock.json dist-interview-ai .vite
}

# 函数：安装依赖
install_deps() {
    echo "📦 安装依赖..."
    npm install
}

# 函数：尝试构建
try_build() {
    local method=$1
    local config=$2
    echo "🏗️ 尝试构建方式: $method"
    
    case $method in
        "element-fix")
            npm run build:element-fix
            ;;
        "legacy")
            npx vite build --config vite.config.legacy.ts
            ;;
        "umd")
            cp index.cdn.html index.html
            npx vite build --config vite.config.umd.ts
            ;;
        "simple")
            npm run build:simple
            ;;
        "force")
            SKIP_OPTIONAL_DEPENDENCIES=true npx vite build --force
            ;;
        *)
            npm run build
            ;;
    esac
}

# 函数：检查构建结果
check_build() {
    if [ -d "dist-interview-ai" ] && [ "$(ls -A dist-interview-ai)" ]; then
        echo "✅ 构建成功！"
        echo "📁 构建产物:"
        ls -la dist-interview-ai/
        return 0
    else
        echo "❌ 构建失败"
        return 1
    fi
}

# 主流程
main() {
    # 1. 清理环境
    cleanup
    
    # 2. 安装依赖
    install_deps
    
    # 3. 修复 Element Plus 版本
    echo "🔧 修复 Element Plus 版本..."
    npm run fix:element
    
    # 4. 尝试不同的构建方式
    build_methods=(
        "element-fix"
        "legacy" 
        "simple"
        "umd"
        "force"
    )
    
    for method in "${build_methods[@]}"; do
        echo ""
        echo "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "=" "="
        try_build "$method"
        
        if check_build; then
            echo "🎉 使用方式 '$method' 构建成功！"
            exit 0
        else
            echo "⚠️ 方式 '$method' 失败，尝试下一个..."
            rm -rf dist-interview-ai
        fi
    done
    
    # 5. 所有方式都失败了
    echo ""
    echo "❌ 所有构建方式都失败了"
    echo ""
    echo "💡 建议的解决方案:"
    echo "1. 升级到 Node.js 18+: nvm install 18 && nvm use 18"
    echo "2. 使用 yarn: yarn install && yarn build"
    echo "3. 手动下载 Element Plus UMD 版本"
    echo "4. 考虑使用其他 UI 库替代 Element Plus"
    echo "5. 联系开发团队获取帮助"
    
    exit 1
}

# 执行主流程
main
