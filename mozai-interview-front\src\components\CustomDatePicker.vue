<template>
  <div class="custom-date-picker">
    <el-input 
      v-model="displayValue" 
      :placeholder="placeholder"
      :disabled="disabled"
      @click="showPicker = true"
      class="date-input"
    >
      <template #prefix>
        <el-icon><Calendar /></el-icon>
      </template>
    </el-input>
    
    <el-popover
      v-model:visible="showPicker"
      trigger="manual"
      placement="bottom-start"
      :width="320"
    >
      <template #reference>
        <div></div>
      </template>
      <div class="date-picker-container">
        <el-date-picker
          ref="datePicker"
          v-model="dateValue"
          type="datetime"
          :placeholder="placeholder"
          format="yyyy-MM-dd HH:mm:ss"
          :shortcuts="shortcuts"
          style="width: 100%"
          @change="handleChange"
        />
        <div class="actions">
          <el-button size="small" @click="showPicker = false">取消</el-button>
          <el-button size="small" type="primary" @click="confirmDate">确定</el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, defineProps, defineEmits } from 'vue'
import { Calendar } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择日期时间'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const showPicker = ref(false)
const dateValue = ref<Date | null>(null)
const displayValue = ref('')

// 设置初始值
onMounted(() => {
  if (props.modelValue) {
    try {
      // 尝试解析传入的日期字符串
      const date = new Date(props.modelValue)
      if (!isNaN(date.getTime())) {
        dateValue.value = date
        displayValue.value = formatDate(date)
      } else {
        // 如果解析失败，使用当前时间
        const now = new Date()
        dateValue.value = now
        displayValue.value = formatDate(now)
        emit('update:modelValue', formatDate(now))
      }
    } catch (error) {
      console.error('初始化日期时出错:', error)
      // 使用当前时间作为默认值
      const now = new Date()
      dateValue.value = now
      displayValue.value = formatDate(now)
      emit('update:modelValue', formatDate(now))
    }
  } else {
    // 如果没有传入初始值，使用当前时间
    const now = new Date()
    dateValue.value = now
    displayValue.value = formatDate(now)
    emit('update:modelValue', formatDate(now))
  }
})

// 日期快捷选项
const shortcuts = [
  {
    text: '今天',
    value: new Date(),
  },
  {
    text: '明天',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + 3600 * 1000 * 24)
      return date
    },
  },
  {
    text: '一周后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + 3600 * 1000 * 24 * 7)
      return date
    },
  },
]

// 格式化日期为字符串
function formatDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 处理日期变更
function handleChange(val: Date | null) {
  if (val) {
    dateValue.value = val
  }
}

// 确认选择的日期
function confirmDate() {
  if (dateValue.value) {
    const formattedDate = formatDate(dateValue.value)
    displayValue.value = formattedDate
    emit('update:modelValue', formattedDate)
    emit('change', formattedDate)
  }
  showPicker.value = false
}

// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && newVal !== displayValue.value) {
    try {
      // 检查是否包含格式错误的标记
      if (newVal.includes('yyyy') || newVal.includes('Fr')) {
        // 使用当前时间替代
        const now = new Date()
        dateValue.value = now
        displayValue.value = formatDate(now)
        emit('update:modelValue', formatDate(now))
      } else {
        // 尝试解析日期字符串
        const date = new Date(newVal)
        if (!isNaN(date.getTime())) {
          dateValue.value = date
          displayValue.value = formatDate(date)
        }
      }
    } catch (error) {
      console.error('解析日期时出错:', error)
    }
  }
})
</script>

<style scoped>
.custom-date-picker {
  position: relative;
  width: 100%;
}

.date-picker-container {
  padding: 10px;
}

.actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 10px;
}
</style> 