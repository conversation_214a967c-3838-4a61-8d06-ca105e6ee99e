<template>
  <div class="interview-list">
    <div class="filter-bar">
      <el-radio-group v-model="currentType" @change="handleTypeChange">
        <el-radio-button :label="InterviewType.MOCK">模拟面试</el-radio-button>
        <el-radio-button :label="InterviewType.FORMAL">正式面试</el-radio-button>
      </el-radio-group>

      <el-button type="primary" @click="handleAddInterview">
        <el-icon><Plus /></el-icon>
        添加{{ currentType === InterviewType.MOCK ? '模拟' : '正式' }}面试
      </el-button>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <div v-else-if="interviews.length === 0" class="empty-container">
      <el-empty description="暂无面试记录" />
    </div>

    <div v-else class="interviews-container">
      <!-- 模拟面试卡片 -->
      <template v-if="currentType === InterviewType.MOCK">
        <el-card v-for="interview in interviews" :key="interview.id" class="interview-card non-clickable">
          <div class="interview-header">
            <h3>{{ interview.position }}</h3>
            <el-tag :type="getStatusType(interview.status)">
              {{ getStatusText(interview.status) }}
            </el-tag>
          </div>

          <div class="interview-info">
            <p><el-icon><Calendar /></el-icon> 面试时间：{{ formatDate(interview.interviewTime) }}</p>
            <p v-if="interview.result !== undefined && interview.status !== 'waiting_result'"><el-icon><InfoFilled /></el-icon> 面试结果：
              <el-tag :type="getResultType(interview.result)">{{ getResultText(interview.result) }}</el-tag>
            </p>
          </div>

          <div v-if="interview.evaluation && interview.status !== 'waiting_result'" class="interview-evaluation">
            <div class="score">
              <span class="label">评分：</span>
              <el-rate
                  v-model="interview.evaluation.score"
                  :max="10"
                  :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                  disabled
                  show-score
                  :score-template="`${interview.evaluation.score}分`"
              />
            </div>
            <div class="feedback">
              <p class="label">反馈：</p>
              <p>{{ interview.evaluation.feedback }}</p>
            </div>
          </div>

          <div class="interview-actions">
            <el-button type="primary" @click="viewInterview(interview)">查看详情</el-button>
            <el-button type="danger" @click="handleDeleteInterview(interview.id)">删除</el-button>
          </div>
        </el-card>
      </template>

      <!-- 正式面试卡片 -->
      <template v-else>
        <el-card v-for="interview in formalInterviews" :key="interview.id" class="interview-card non-clickable">
          <div class="interview-header">
            <h3>{{ interview.company }}</h3>
            <el-tag :type="getStatusType(interview.status)">
              {{ getStatusText(interview.status) }}
            </el-tag>
          </div>

          <div class="interview-info">
            <p><el-icon><Briefcase /></el-icon> 面试岗位：{{ interview.position }}</p>
            <p><el-icon><Calendar /></el-icon> 面试时间：{{ formatDate(interview.interviewTime) }}</p>
            <p v-if="interview.result !== undefined"><el-icon><InfoFilled /></el-icon> 面试结果：
              <el-tag :type="getResultType(interview.result)">{{ getResultText(interview.result) }}</el-tag>
            </p>
          </div>

          <div v-if="interview.videoUrl" class="interview-media">
            <el-button type="success" size="small" @click.stop="playMedia(interview)">
              <el-icon><VideoPlay /></el-icon> 播放面试录音/视频
            </el-button>
          </div>

          <div v-if="interview.evaluation && interview.status !== 'waiting_result'" class="interview-evaluation">
            <div class="score">
              <span class="label">评分：</span>
              <el-rate
                  v-model="interview.evaluation.score"
                  :max="10"
                  :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                  disabled
                  show-score
                  :score-template="`${interview.evaluation.score}分`"
              />
            </div>
            <div class="feedback">
              <p class="label">反馈：</p>
              <p>{{ interview.evaluation.feedback }}</p>
            </div>
          </div>

          <div class="interview-actions">
            <el-button type="primary" @click="viewInterview(interview)">查看详情</el-button>
            <el-button type="danger" @click="handleDeleteInterview(interview.id)">删除</el-button>
          </div>
        </el-card>
      </template>
    </div>

    <!-- 面试详情对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="getInterviewTitle(currentInterview)"
        width="60%"
    >
      <div v-if="currentInterview" class="interview-detail">
        <template v-if="currentInterview.type === InterviewType.FORMAL">
          <div class="detail-item">
            <span class="label">面试公司：</span>
            <span>{{ (currentInterview as FormalInterview).company }}</span>
          </div>
        </template>

        <div class="detail-item">
          <span class="label">面试岗位：</span>
          <span>{{ currentInterview.position }}</span>
        </div>

        <div class="detail-item">
          <span class="label">面试时间：</span>
          <span>{{ formatDate(currentInterview.interviewTime) }}</span>
        </div>

        <div class="detail-item">
          <span class="label">面试状态：</span>
          <el-tag :type="getStatusType(currentInterview.status)">
            {{ getStatusText(currentInterview.status) }}
          </el-tag>
        </div>

        <div v-if="currentInterview.result !== undefined" class="detail-item">
          <span class="label">面试结果：</span>
          <el-tag :type="getResultType(currentInterview.result)">
            {{ getResultText(currentInterview.result) }}
          </el-tag>
        </div>

        <template v-if="currentInterview.evaluation && currentInterview.status !== 'waiting_result'">
          <div class="detail-item">
            <span class="label">评分：</span>
            <el-rate
                v-model="currentInterview.evaluation.score"
                :max="10"
                :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                disabled
                show-score
                :score-template="`${currentInterview.evaluation.score}分`"
            />
          </div>

          <div class="detail-item">
            <span class="label">反馈：</span>
            <p>{{ currentInterview.evaluation.feedback }}</p>
          </div>

          <div class="detail-item">
            <span class="label">优势：</span>
            <div class="tags">
              <el-tag
                  v-for="(strength, index) in currentInterview.evaluation.strengths"
                  :key="index"
                  type="success"
                  effect="light"
                  class="tag"
              >
                {{ strength }}
              </el-tag>
            </div>
          </div>

          <div class="detail-item">
            <span class="label">改进建议：</span>
            <div class="tags">
              <el-tag
                  v-for="(improvement, index) in currentInterview.evaluation.improvements"
                  :key="index"
                  type="warning"
                  effect="light"
                  class="tag"
              >
                {{ improvement }}
              </el-tag>
            </div>
          </div>
        </template>

        <div v-if="currentInterview.videoUrl" class="detail-item">
          <span class="label">面试录音/视频：</span>
          <div class="media-player">
            <video v-if="isVideoFile(currentInterview.videoUrl)" controls :src="currentInterview.videoUrl"></video>
            <audio v-else controls :src="currentInterview.videoUrl"></audio>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加面试对话框 -->
    <el-dialog
        v-model="addDialogVisible"
        :title="`添加${currentType === InterviewType.MOCK ? '模拟' : '正式'}面试`"
        width="50%"
    >
      <template v-if="currentType === InterviewType.MOCK">
        <!-- 模拟面试表单 -->
        <mock-interview-form @success="handleAddSuccess" />
      </template>
      <template v-else>
        <!-- 正式面试表单 -->
        <formal-interview-upload @success="handleAddSuccess" />
      </template>
    </el-dialog>

    <!-- 媒体播放对话框 -->
    <el-dialog
        v-model="mediaDialogVisible"
        title="面试录音/视频"
        width="70%"
    >
      <div v-if="currentInterview?.videoUrl" class="media-player-container">
        <video v-if="isVideoFile(currentInterview.videoUrl)" controls :src="currentInterview.videoUrl"></video>
        <audio v-else controls :src="currentInterview.videoUrl"></audio>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Calendar, Briefcase, VideoPlay, Plus, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getInterviewsByType, deleteInterview as apiDeleteInterview } from '@/api/interview'
import type { Interview, FormalInterview } from '@/types/interview'
import { InterviewType } from '@/types/interview'
import FormalInterviewUpload from './FormalInterviewUpload.vue'
import MockInterviewForm from './MockInterviewForm.vue'
import { formatDate } from '@/utils/dateFormat'

// 当前选中的面试类型
const currentType = ref(InterviewType.MOCK)

// 面试列表
const interviews = ref<Interview[]>([])

// 计算属性：正式面试列表
const formalInterviews = computed(() => {
  return interviews.value.filter(
      interview => interview.type === InterviewType.FORMAL
  ) as FormalInterview[]
})

// 加载状态
const loading = ref(false)

// 当前选中的面试
const currentInterview = ref<Interview | null>(null)

// 对话框显示状态
const dialogVisible = ref(false)
const addDialogVisible = ref(false)
const mediaDialogVisible = ref(false)

// 获取面试标题
const getInterviewTitle = (interview: Interview | null): string => {
  if (!interview) return '面试详情'

  if (interview.type === InterviewType.FORMAL) {
    return (interview as FormalInterview).company
  } else {
    return interview.position
  }
}

// 加载面试列表
const loadInterviews = async () => {
  loading.value = true
  try {
    interviews.value = await getInterviewsByType(currentType.value)
  } catch (error) {
    console.error('加载面试列表失败:', error)
    ElMessage.error('加载面试列表失败')
  } finally {
    loading.value = false
  }
}

// 处理类型变更
const handleTypeChange = () => {
  loadInterviews()
}

// 查看面试详情
const viewInterview = (interview: Interview) => {
  currentInterview.value = interview
  dialogVisible.value = true
}

// 播放媒体文件
const playMedia = (interview: Interview) => {
  currentInterview.value = interview
  mediaDialogVisible.value = true
}

// 删除面试
const handleDeleteInterview = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这条面试记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const success = await apiDeleteInterview(id)

    if (success) {
      ElMessage.success('删除成功')
      // 重新加载面试列表
      loadInterviews()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (error) {
    // 用户取消删除操作
    console.log('用户取消删除操作')
  }
}

// 添加面试
const handleAddInterview = () => {
  addDialogVisible.value = true
}

// 添加成功回调
const handleAddSuccess = () => {
  addDialogVisible.value = false
  loadInterviews()
  ElMessage.success('添加成功')
}

// 判断是否为视频文件
const isVideoFile = (url: string) => {
  const videoExtensions = ['.mp4', '.webm', '.ogg']
  return videoExtensions.some(ext => url.toLowerCase().endsWith(ext))
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'in_progress':
      return 'primary'
    case 'waiting_result':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取面试结果文本
const getResultText = (result: number | undefined | null) => {
  if (result === undefined || result === null) return '未评定'
  return result === 1 ? '面试通过' : '面试未通过'
}

// 获取面试结果标签类型
const getResultType = (result: number | undefined | null) => {
  if (result === undefined || result === null) return 'info'
  return result === 1 ? 'success' : 'danger'
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'in_progress':
      return '进行中'
    case 'waiting_result':
      return '等待结果'
    default:
      return '待处理'
  }
}

// 组件挂载时加载面试列表
onMounted(() => {
  loadInterviews()
})
</script>

<style scoped>
.interview-list {
  width: 100%;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.loading-container, .empty-container {
  padding: 40px;
  text-align: center;
}

.interviews-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.interview-card {
  margin-bottom: 10px;
}

.non-clickable {
  cursor: default;
}

.interview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.interview-header h3 {
  margin: 0;
  font-size: 18px;
}

.interview-info {
  margin-bottom: 15px;
}

.interview-info p {
  display: flex;
  align-items: center;
  margin: 5px 0;
}

.interview-info .el-icon {
  margin-right: 5px;
}

.interview-media {
  margin-bottom: 15px;
}

.interview-evaluation {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.interview-evaluation .score {
  margin-bottom: 10px;
}

.interview-evaluation .label {
  font-weight: bold;
}

.interview-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.interview-detail .detail-item {
  margin-bottom: 15px;
}

.interview-detail .label {
  font-weight: bold;
  margin-right: 10px;
}

.interview-detail .tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px;
}

.media-player-container {
  display: flex;
  justify-content: center;
}

.media-player-container video,
.media-player-container audio,
.media-player video,
.media-player audio {
  width: 100%;
}
</style> 