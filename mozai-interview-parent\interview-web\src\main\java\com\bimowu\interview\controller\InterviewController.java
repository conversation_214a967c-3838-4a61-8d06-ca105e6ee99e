package com.bimowu.interview.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bimowu.interview.base.Constant;
import com.bimowu.interview.model.Interview;
import com.bimowu.interview.model.InterviewTranscription;
import com.bimowu.interview.model.ResumeProjectQuestion;
import com.bimowu.interview.model.ResumeCategoryRelation;
import com.bimowu.interview.model.ResumeProgress;
import com.bimowu.interview.model.SysTodo;
import com.bimowu.interview.service.InterviewService;
import com.bimowu.interview.service.InterviewTranscriptionService;
import com.bimowu.interview.service.ResumeProjectQuestionService;
import com.bimowu.interview.service.TranscriptionService;
import com.bimowu.interview.service.ResumeCategoryRelationService;
import com.bimowu.interview.service.ResumeProgressService;
import com.bimowu.interview.utils.OssUtils;
import com.bimowu.interview.utils.RedisUtil;
import com.bimowu.interview.service.impl.AiManager;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import com.bimowu.interview.dao.SysTodoMapper;

/**
 * 面试控制器
 */
@Slf4j
@RestController
@RequestMapping("/interviews")
public class InterviewController {

    @Autowired
    private InterviewService interviewService;

    @Autowired
    private InterviewTranscriptionService transcriptionService;

    @Autowired
    private ResumeProjectQuestionService resumeProjectQuestionService;
    
    @Autowired
    private ResumeCategoryRelationService resumeCategoryRelationService;
    
    @Autowired
    private ResumeProgressService resumeProgressService;

    @Autowired
    private SysTodoMapper sysTodoMapper;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private AiManager aiManager;

    @Autowired
    private TranscriptionService transService;

    @Autowired
    private RedisUtil redisUtil;


    /**
     * 创建面试
     *
     * @param interview 面试信息
     * @return 面试ID
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createInterview(@RequestBody Interview interview,@RequestHeader Integer userId) {
        log.info("创建面试: {}", interview);
        //根据userId和面试岗位取到resumeId,根据resumeId+userId获取到当前的进度，判断当前进度和上传的面试环节是否一致，不一致返回"您当前处于xx阶段，请进行xx"

        interview.setUserId(userId);
        // 2. 用 get 读取用户信息
        HashMap<String,Object> userInfo = (HashMap<String, Object>) redisUtil.get(String.format(Constant.REDIS_KEY, userId));
        interview.setCandidateName((String) userInfo.get("nickname"));
        Map<String, Object> response = new HashMap<>();
        String interviewId = "";

        try {
            if(StringUtils.equals(interview.getType(),"mock")){
                // 根据userId和面试岗位获取resumeId
                LambdaQueryWrapper<ResumeCategoryRelation> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ResumeCategoryRelation::getUserId, userId.longValue())
                       .eq(ResumeCategoryRelation::getCatId, Long.valueOf(interview.getPosition()
                       )).eq(ResumeCategoryRelation::getIsDelete, 0);


                ResumeCategoryRelation relation = resumeCategoryRelationService.getOne(wrapper);
                if (relation == null) {
                    log.error("未找到对应的简历信息，userId={}, position={}", userId, interview.getPosition());
                    response.put("code", 400);
                    response.put("message", "您还未创建简历，请先到简历系统创建对应简历");
                    response.put("data", null);
                    return ResponseEntity.ok(response);
                }

                Long resumeId = relation.getResumeId();

                // 根据resumeId和userId获取当前进度
                Map<String, Object> params = new HashMap<>();
                params.put("userId", userId);
                params.put("resumeId", resumeId);
                List<ResumeProgress> progressList = resumeProgressService.getResumeProgressByCondition(params);

                if (progressList == null || progressList.isEmpty()) {
                    log.error("未找到对应的进度信息，userId={}, resumeId={}", userId, resumeId);
                    response.put("code", 400);
                    response.put("message", "未找到您的学习进度信息，请先完成简历创建");
                    response.put("data", null);
                    return ResponseEntity.ok(response);
                }

                ResumeProgress progress = progressList.get(0);
                String currentStage = progress.getCurrentStage();

                // 判断当前进度和上传的面试环节是否一致
                String interviewStage = interview.getStage();
                boolean isStageMatch = false;

                // 当前进度阶段与面试环节的映射关系
                // currentStage: 0-创建简历, 1-知识学习, 2-hr面试, 3-技术面试, 4-正式面试
                // interviewStage: hr/tech
                if ("2".equals(currentStage) && "hr".equals(interviewStage)) {
                    isStageMatch = true;
                } else if ("3".equals(currentStage) && "tech".equals(interviewStage)) {
                    isStageMatch = true;
                }


                if (!isStageMatch) {
                    String stageName;
                    String expectedInterview;

                    switch (currentStage) {
                        case "0":
                            stageName = "创建简历";
                            expectedInterview = "请先完成简历创建";
                            break;
                        case "1":
                            stageName = "知识学习";
                            expectedInterview = "请先完成知识学习";
                            break;
                        case "2":
                            stageName = "HR面试";
                            expectedInterview = "请进行HR面试";
                            break;
                        case "3":
                            stageName = "技术面试";
                            expectedInterview = "请进行技术面试";
                            break;
                        case "4":
                            stageName = "正式面试";
                            expectedInterview = "请进行正式面试";
                            break;
                        default:
                            stageName = "未知阶段";
                            expectedInterview = "请按照学习路径进行";
                    }

                    log.error("面试环节与当前进度不匹配，currentStage={}, interviewStage={}", currentStage, interviewStage);
                    response.put("code", 400);
                    response.put("message", "您当前处于" + stageName + "阶段，" + expectedInterview);
                    response.put("data", null);
                    return ResponseEntity.ok(response);
                }else{
                    //查询当前阶段的待办是否已完成，已完成则拒绝面试，提示“你已完成xx面试，请耐心等待结果！”
                    Integer todoType = null;
                    String interviewTypeName = "";
                    // 根据面试阶段确定待办类型
                    if ("hr".equals(interviewStage)) {
                        todoType = 2; // hr面试
                        interviewTypeName = "HR面试";
                    } else if ("tech".equals(interviewStage)) {
                        todoType = 3; // 技术面试
                        interviewTypeName = "技术面试";
                    }
                    if (todoType != null) {
                        // 查询当前用户该类型的待办是否有未完成的，有则继续，没有则拒绝
                        List<SysTodo> completedTodos = sysTodoMapper.selectCompletedByUserIdAndTodoTypeAndResumeId(userId.longValue(), todoType, resumeId);
                        if (CollectionUtils.isEmpty (completedTodos)) {
                            log.info("用户已完成{}，拒绝重复面试，userId={}, todoType={}", interviewTypeName, userId, todoType);
                            response.put("code", 400);
                            response.put("message", "你已完成" + interviewTypeName + "，请耐心等待结果！");
                            response.put("data", null);
                            return ResponseEntity.ok(response);
                        }
                    }
                }
            }
            interviewId = interviewService.createInterview(interview);
            // 使用统一的响应格式
            response.put("code", 0);  // 0表示成功
            response.put("message", "创建面试成功");
            Map<String, Object> data = new HashMap<>();
            data.put("interviewId", interviewId);
            response.put("data", data);

        } catch (Exception e) {
            log.error("创建面试时发生异常", e.getMessage());
            response.put("code", 500);  // 500表示服务器错误
            response.put("message", e.getMessage());
            response.put("data", null);
        }
        return ResponseEntity.ok(response);
    }


    /**
     * 更新面试信息
     *
     * @param interviewId 面试ID
     * @param interview   面试信息
     * @return 更新结果
     */
    @PutMapping("/{interviewId}")
    public ResponseEntity<Map<String, Object>> updateInterview(@PathVariable String interviewId, @RequestBody Interview interview) {

        log.info("更新面试: id={}", interviewId);
        interview.setId(interviewId);

        Map<String, Object> response = new HashMap<>();

        try {
            boolean success = interviewService.updateInterview(interview);
            response.put("success", success);
            if (!success) {
                response.put("message", "更新面试失败");
            }
        } catch (Exception e) {
            log.error("更新面试时发生异常", e);
            response.put("success", false);
            response.put("message", "更新面试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 获取面试信息
     *
     * @param interviewId 面试ID
     * @return 面试信息
     */
    @GetMapping("/{interviewId}")
    public ResponseEntity<Map<String, Object>> getInterviewById(@PathVariable String interviewId) {
        log.info("获取面试: id={}", interviewId);

        Map<String, Object> response = new HashMap<>();

        try {
            Interview interview = interviewService.getById(interviewId);

            if (interview != null) {
                // 获取转写记录
                LambdaQueryWrapper<InterviewTranscription> wrapper = new LambdaQueryWrapper<InterviewTranscription>().eq(InterviewTranscription::getInterviewId, interviewId);

                List<InterviewTranscription> transcriptions = transcriptionService.list(wrapper);
                HashMap<Integer, String> questions = new HashMap<>();
                HashMap<Integer, String> transcription = new HashMap<>();
                for (int i = 0; i < transcriptions.size(); i++) {
                    questions.put(i, transcriptions.get(i).getQuestion());
                    transcription.put(i, transcriptions.get(i).getTranscription());
                }

                List<String> answerList = new LinkedList<>();
                for (String q : questions.values()) {
                    LambdaQueryWrapper<ResumeProjectQuestion> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(ResumeProjectQuestion::getQuestion, q);
                    answerList.add(resumeProjectQuestionService.getOne(queryWrapper).getAnswer());
                }
                Map<Integer, String> answer = IntStream.range(0, answerList.size()).boxed().collect(Collectors.toMap(i -> i,                   // 键：索引
                        answerList::get,          // 值：列表元素
                        (existing, replacement) -> existing, // 冲突策略（通常不会冲突）
                        HashMap::new              // 指定 Map 实现
                ));
                response.put("success", true);
                response.put("interview", interview);
                response.put("questions", questions);
                response.put("transcriptions", transcription);
                response.put("answer", answer);
            } else {
                response.put("success", false);
                response.put("message", "未找到面试信息");
            }
        } catch (Exception e) {
            log.error("获取面试时发生异常", e);
            response.put("success", false);
            response.put("message", "获取面试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 获取面试详情（包含完整转写记录）
     *
     * @param interviewId 面试ID
     * @return 面试信息和转写记录
     */
    @GetMapping("/{interviewId}/detail")
    public ResponseEntity<Map<String, Object>> getInterviewDetail(@PathVariable String interviewId) {
        log.info("获取面试详情: id={}", interviewId);

        Map<String, Object> response = new HashMap<>();

        try {
            Interview interview = interviewService.getById(interviewId);

            if (interview != null) {
                // 获取转写记录
                LambdaQueryWrapper<InterviewTranscription> wrapper = new LambdaQueryWrapper<InterviewTranscription>().eq(InterviewTranscription::getInterviewId, interviewId);
                List<InterviewTranscription> transcriptions = transcriptionService.list(wrapper);
                response.put("success", true);
                response.put("interview", interview);
                response.put("transcriptions", transcriptions);
            } else {
                response.put("success", false);
                response.put("message", "未找到面试信息");
            }
        } catch (Exception e) {
            log.error("获取面试详情时发生异常", e);
            response.put("success", false);
            response.put("message", "获取面试详情失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 查询面试列表
     *
     * @param params 查询条件
     * @return 面试列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getInterviewsByCondition(@RequestParam Map<String, Object> params) {
        log.info("查询面试列表: params={}", params);

        Map<String, Object> response = new HashMap<>();

        try {
            List<Interview> interviews = interviewService.getInterviewsByCondition(params);
            response.put("success", true);
            response.put("interviews", interviews);
            response.put("total", interviews.size());
        } catch (Exception e) {
            log.error("查询面试列表时发生异常", e);
            response.put("success", false);
            response.put("message", "查询面试列表失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 根据类型获取面试列表
     *
     * @param type 面试类型
     * @return 面试列表
     */
    @GetMapping("/type/{type}")
    public ResponseEntity<Map<String, Object>> getInterviewsByType(@PathVariable String type) {
        log.info("根据类型查询面试列表: type={}", type);

        Map<String, Object> response = new HashMap<>();

        try {
//            List<Interview> interviews = interviewService.getInterviewsByType(type);
            LambdaQueryWrapper<Interview> wrapper = new LambdaQueryWrapper<Interview>().eq(Interview::getType, type).orderByDesc(Interview::getCreateTime);
            List<Interview> interviews = interviewService.list(wrapper);

            // 使用统一的响应格式
            response.put("code", 0);  // 0表示成功
            response.put("message", "获取成功");
            response.put("data", interviews);
        } catch (Exception e) {
            log.error("根据类型查询面试列表时发生异常", e);
            response.put("code", 500);  // 500表示服务器错误
            response.put("message", "查询面试列表失败: " + e.getMessage());
            response.put("data", null);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 更新面试状态
     *
     * @param interviewId 面试ID
     * @param status      面试状态
     * @return 更新结果
     */
    @PutMapping("/{interviewId}/status")
    public ResponseEntity<Map<String, Object>> updateInterviewStatus(@PathVariable String interviewId, @RequestParam Integer status) {

        log.info("更新面试状态: id={}, status={}", interviewId, status);

        Map<String, Object> response = new HashMap<>();

        try {
            boolean success = interviewService.updateInterviewStatus(interviewId, status);
            response.put("success", success);
            if (!success) {
                response.put("message", "更新面试状态失败");
            }
        } catch (Exception e) {
            log.error("更新面试状态时发生异常", e);
            response.put("success", false);
            response.put("message", "更新面试状态失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 更新面试结果
     *
     * @param interviewId 面试ID
=     * @return 更新结果
     */
    @PutMapping("/{interviewId}/result")
    public ResponseEntity<Map<String, Object>> updateInterviewResult(
            @PathVariable String interviewId, 
            @RequestBody Map<String, Object> resultMap) {
        
        log.info("更新面试结果: id={}, result={}", interviewId, resultMap);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Integer overallScore = (Integer) resultMap.get("overallScore");
            String feedback = (String) resultMap.get("feedback");
            String strengths = (String) resultMap.get("strengths");
            String improvements = (String) resultMap.get("improvements");
            
            boolean success = interviewService.updateInterviewResult(
                    interviewId, overallScore, feedback, strengths, improvements);
            
            response.put("success", success);
            if (!success) {
                response.put("message", "更新面试结果失败");
            }
        } catch (Exception e) {
            log.error("更新面试结果时发生异常", e);
            response.put("success", false);
            response.put("message", "更新面试结果失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 删除面试
     *
     * @param interviewId 面试ID
     * @return 删除结果
     */
    @DeleteMapping("/{interviewId}")
    public ResponseEntity<Map<String, Object>> deleteInterview(@PathVariable String interviewId) {
        log.info("删除面试: id={}", interviewId);

        Map<String, Object> response = new HashMap<>();

        try {
            boolean success = interviewService.deleteInterview(interviewId);
            response.put("success", success);
            if (!success) {
                response.put("message", "删除面试失败");
            }
        } catch (Exception e) {
            log.error("删除面试时发生异常", e);
            response.put("success", false);
            response.put("message", "删除面试失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 保存面试问题列表
     *
     * @param interviewId 面试ID
     * @param questions   问题列表
     * @return 保存结果
     */
    @PostMapping("/{interviewId}/questions")
    public ResponseEntity<Map<String, Object>> saveInterviewQuestions(@PathVariable String interviewId, @RequestBody List<String> questions) {

        log.info("保存面试问题列表: id={}, count={}", interviewId, questions.size());

        Map<String, Object> response = new HashMap<>();

        try {
            boolean success = interviewService.saveInterviewQuestions(interviewId, questions);
            response.put("success", success);
            if (!success) {
                response.put("message", "保存面试问题列表失败");
            }
        } catch (Exception e) {
            log.error("保存面试问题列表时发生异常", e);
            response.put("success", false);
            response.put("message", "保存面试问题列表失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 获取面试问题列表
     *
     * @param interviewId 面试ID
     * @return 问题列表
     */
    @GetMapping("/{interviewId}/questions")
    public ResponseEntity<Map<String, Object>> getInterviewQuestions(@PathVariable String interviewId) {
        log.info("获取面试问题列表: id={}", interviewId);

        Map<String, Object> response = new HashMap<>();


        try {
            List<String> questions = interviewService.getInterviewQuestions(interviewId);

            List<String> list = new ArrayList<>();
            ObjectMapper mapper = new ObjectMapper();
            for (String jsonStr : questions) {
                try {
                    JsonNode node = mapper.readTree(jsonStr);
                    String question = node.get("question").asText();
                    list.add(question);
                } catch (Exception e) {
                    e.printStackTrace(); // 处理解析异常
                }
            }

            if (questions != null) {
                response.put("success", true);
                response.put("questions", list);
            } else {
                response.put("success", false);
                response.put("message", "未找到面试问题列表");
            }
        } catch (Exception e) {
            log.error("获取面试问题列表时发生异常", e);
            response.put("success", false);
            response.put("message", "获取面试问题列表失败: " + e.getMessage());
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 上传面试视频
     *
     * @param interviewId 面试ID
     * @param videoFile   视频文件
     * @return 上传结果
     */
    @PostMapping("/{interviewId}/video")
    public ResponseEntity<Map<String, Object>> uploadInterviewVideo(@PathVariable("interviewId") String interviewId, @RequestParam("video") MultipartFile videoFile) {
        log.info("开始处理上传面试视频请求: interviewId={}, 文件名={}, 文件大小={}B", interviewId, videoFile.getOriginalFilename(), videoFile.getSize());
        Map<String, Object> response = new HashMap<>();

        try {
            // 检查面试ID是否存在
            Interview interview = interviewService.getInterviewById(interviewId);
            if (interview == null) {
                log.error("上传视频失败: 面试ID不存在: {}", interviewId);
                response.put("code", 404);
                response.put("message", "上传视频失败: 面试ID不存在");
                response.put("data", null);
                return ResponseEntity.ok(response);
            }

            // 检查文件是否为空
            if (videoFile.isEmpty()) {
                log.error("上传视频失败: 视频文件为空");
                response.put("code", 400);
                response.put("message", "视频文件不能为空");
                response.put("data", null);
                return ResponseEntity.ok(response);
            }

            // 检查文件类型
            String originalFilename = videoFile.getOriginalFilename();
            if (!isVideoFile(originalFilename)) {
                log.error("上传视频失败: 不支持的文件格式: {}", originalFilename);
                response.put("code", 400);
                response.put("message", "不支持的文件格式，请上传MP4、AVI等视频文件");
                response.put("data", null);
                return ResponseEntity.ok(response);
            }

            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始上传视频文件到OSS: interviewId={}, 文件名={}", interviewId, originalFilename);

                    // 上传视频到OSS
                    String videoUrl = ossUtils.uploadVideo(videoFile);
                    log.info("视频文件上传到OSS成功: interviewId={}, videoUrl={}", interviewId, videoUrl);

                    // 更新面试信息中的视频URL
                    Interview interview1 = new Interview();
                    interview1.setId(interviewId);
                    interview1.setVideoUrl(videoUrl);
                    interview1.setStatus(1);
                    boolean result = interviewService.updateById(interview1);

                    String dialogTaskId = transService.dialogTranslate(videoUrl);
                    String chapterTaskId = transService.chapterTranslate(videoUrl);
                    String qaTaskId = transService.questionsAnsweringTranslate(videoUrl);
                    List<Object> dialogList = transService.transcriptionResult(dialogTaskId,"dialog",interviewId);
                    List<Object> chapterList = transService.transcriptionResult(chapterTaskId,"chapter",interviewId);
                    List<Object> qaList = transService.transcriptionResult(qaTaskId,"qa",interviewId);

                    // 保存对话记录和章节记录到数据库
                    if (dialogList != null && !dialogList.isEmpty()) {
                        boolean saveDialogResult = interviewService.saveDialogRecords(dialogList, interviewId);
                        log.info("保存面试对话记录结果: {}", saveDialogResult ? "成功" : "失败");
                    }

                    if (chapterList != null && !chapterList.isEmpty()) {
                        boolean saveChapterResult = interviewService.saveChapterRecords(chapterList, interviewId);
                        log.info("保存面试章节记录结果: {}", saveChapterResult ? "成功" : "失败");
                    }

                    // 保存问答转写记录到数据库
                    if (qaList != null && !qaList.isEmpty()) {
                        boolean saveQaResult = interviewService.saveTranscriptionRecords(qaList, interviewId);
                        log.info("保存面试问答记录结果: {}", saveQaResult ? "成功" : "失败");
                    }

                }catch (Exception e){
                    e.printStackTrace();
                }

            });

        } catch (Exception e) {
            log.error("上传面试视频时发生IO异常: {}", e.getMessage(), e);
            response.put("code", 500);
            response.put("message", "上传视频失败: " + e.getMessage());
            response.put("data", null);
        }
        response.put("code", 0);
        response.put("message", "视频上传成功");
        return ResponseEntity.ok(response);
    }

    /**
     * 上传面试视频URL（前端OSS直传后调用）
     *
     * @param interviewId 面试ID
     * @param requestBody 请求体，包含videoUrl
     * @return 响应结果
     */
    @PostMapping("/{interviewId}/video-url")
    public ResponseEntity<Map<String, Object>> updateInterviewVideoUrl(
            @PathVariable("interviewId") String interviewId,
            @RequestBody Map<String, String> requestBody) {
        log.info("接收前端直传OSS后的视频URL: interviewId={}, requestBody={}", interviewId, requestBody);
        Map<String, Object> response = new HashMap<>();

        try {
            // 检查面试ID是否存在
            Interview interview = interviewService.getInterviewById(interviewId);



            if (interview == null) {
                log.error("更新视频URL失败: 面试ID不存在: {}", interviewId);
                response.put("code", 404);
                response.put("message", "更新视频URL失败: 面试ID不存在");
                response.put("data", false);
                return ResponseEntity.ok(response);
            }

            // 获取视频URL
            String videoUrl = requestBody.get("videoUrl");
            if (videoUrl == null || videoUrl.isEmpty()) {
                log.error("更新视频URL失败: 视频URL为空");
                response.put("code", 400);
                response.put("message", "视频URL不能为空");
                response.put("data", false);
                return ResponseEntity.ok(response);
            }

            // 更新面试信息中的视频URL
            Interview updatedInterview = new Interview();
            updatedInterview.setId(interviewId);
            updatedInterview.setVideoUrl(videoUrl);
            updatedInterview.setStatus(1); // 设置状态为已上传视频
            boolean result = interviewService.updateById(updatedInterview);
            //正式面试---模拟面试在面试过程中完成语音转文字
            if(interview.getType().equals("formal")){
                // 异步处理视频转写
                CompletableFuture.runAsync(() -> {
                    try {
                        log.info("开始处理视频转写: interviewId={}, videoUrl={}", interviewId, videoUrl);
                        String dialogTaskId = transService.dialogTranslate(videoUrl);
                        String chapterTaskId = transService.chapterTranslate(videoUrl);
                        String qaTaskId = transService.questionsAnsweringTranslate(videoUrl);
                        List<Object> dialogList = transService.transcriptionResult(dialogTaskId, "dialog", interviewId);
                        List<Object> chapterList = transService.transcriptionResult(chapterTaskId, "chapter", interviewId);
                        List<Object> qaList = transService.transcriptionResult(qaTaskId, "qa", interviewId);

                        // 保存对话记录和章节记录到数据库
                        if (dialogList != null && !dialogList.isEmpty()) {
                            boolean saveDialogResult = interviewService.saveDialogRecords(dialogList, interviewId);
                            log.info("保存面试对话记录结果: {}", saveDialogResult ? "成功" : "失败");
                        }

                        if (chapterList != null && !chapterList.isEmpty()) {
                            boolean saveChapterResult = interviewService.saveChapterRecords(chapterList, interviewId);
                            log.info("保存面试章节记录结果: {}", saveChapterResult ? "成功" : "失败");
                        }

                        if (qaList != null && !qaList.isEmpty()) {
                            boolean saveQaResult = interviewService.saveTranscriptionRecords(qaList, interviewId);
                            log.info("保存面试问答记录结果: {}", saveQaResult ? "成功" : "失败");
                        }
                    } catch (Exception e) {
                        log.error("处理视频转写失败: interviewId={}, 异常: {}", interviewId, e.getMessage(), e);
                    }
                });
            }
            response.put("code", result ? 0 : 500);
            response.put("message", result ? "更新视频URL成功" : "更新视频URL失败");
            response.put("data", result);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("更新视频URL异常: interviewId={}, 异常: {}", interviewId, e.getMessage(), e);
            response.put("code", 500);
            response.put("message", "更新视频URL异常: " + e.getMessage());
            response.put("data", false);
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 判断文件是否为视频文件
     *
     * @param filename 文件名
     * @return 是否为视频文件
     */
    private boolean isVideoFile(String filename) {
        if (filename == null) {
            return false;
        }
        String lowerFilename = filename.toLowerCase();
        return lowerFilename.endsWith(".mp4") || lowerFilename.endsWith(".avi") || lowerFilename.endsWith(".mov") || lowerFilename.endsWith(".wmv") || lowerFilename.endsWith(".flv") || lowerFilename.endsWith(".mkv");
    }
} 