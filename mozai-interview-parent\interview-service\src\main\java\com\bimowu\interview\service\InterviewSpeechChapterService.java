package com.bimowu.interview.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.interview.model.InterviewSpeechChapter;

import java.util.List;
import java.util.Map;

/**
 * 面试语音转文字章节段落记录服务接口
 */
public interface InterviewSpeechChapterService extends IService<InterviewSpeechChapter> {
    
    /**
     * 保存章节记录
     * 
     * @param chapter 章节记录
     * @return 记录ID
     */
    Long saveChapter(InterviewSpeechChapter chapter);
    
    /**
     * 批量保存章节记录
     * 
     * @param chapters 章节记录列表
     * @return 是否成功
     */
    boolean batchSaveChapters(List<InterviewSpeechChapter> chapters);
    
    /**
     * 更新章节记录
     * 
     * @param chapter 章节记录
     * @return 是否成功
     */
    boolean updateChapter(InterviewSpeechChapter chapter);
    
    /**
     * 获取章节记录
     * 
     * @param id 记录ID
     * @return 章节记录
     */
    InterviewSpeechChapter getChapterById(Long id);
    
    /**
     * 根据面试ID获取章节记录列表
     * 
     * @param interviewId 面试ID
     * @return 章节记录列表
     */
    List<InterviewSpeechChapter> getChaptersByInterviewId(String interviewId);
    
    /**
     * 根据面试ID和时间范围获取章节记录
     * 
     * @param interviewId 面试ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 章节记录列表
     */
    List<InterviewSpeechChapter> getChaptersByTimeRange(String interviewId, Integer startTime, Integer endTime);
    
    /**
     * 根据条件查询章节记录列表
     * 
     * @param params 查询条件
     * @return 章节记录列表
     */
    List<InterviewSpeechChapter> getChaptersByCondition(Map<String, Object> params);
    
    /**
     * 删除章节记录
     * 
     * @param id 记录ID
     * @return 是否成功
     */
    boolean deleteChapter(Long id);
    
    /**
     * 删除面试的所有章节记录
     * 
     * @param interviewId 面试ID
     * @return 是否成功
     */
    boolean deleteChaptersByInterviewId(String interviewId);
} 