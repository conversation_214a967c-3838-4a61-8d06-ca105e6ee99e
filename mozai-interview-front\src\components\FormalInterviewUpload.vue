<template>
  <div class="formal-interview-upload">
    <h2>上传正式面试</h2>

    <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="面试公司" prop="company">
        <el-input v-model="formData.company" placeholder="请输入面试公司"></el-input>
      </el-form-item>

      <el-form-item label="面试岗位" prop="position">
        <el-select v-model="formData.position" placeholder="请选择面试岗位">
          <el-option label="开发" value="开发"></el-option>
          <el-option label="技术支持" value="技术支持"></el-option>
          <el-option label="测试" value="测试"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="面试时间" prop="interviewTime">
        <CustomDatePicker
            v-model="formData.interviewTime"
            placeholder="请选择面试时间"
            @change="handleCustomDateChange"
        />
      </el-form-item>

      <el-form-item label="面试音视频" prop="mediaFile">
        <el-upload
            class="upload-demo"
            :action="'#'"
            :auto-upload="false"
            :on-change="handleFileChange"
            :limit="1"
            :file-list="fileList"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">支持mp3/mp4文件，大小不超过500MB</div>
          </template>
        </el-upload>
        
        <!-- OSS直传提示 -->
        <div v-if="isOssAvailable" class="upload-tip success">
          <i class="el-icon-check"></i> 将使用OSS直传上传文件
        </div>
        <div v-else class="upload-tip error">
          <i class="el-icon-warning"></i> OSS直传功能不可用，请联系管理员处理
        </div>
        
        <!-- 上传模式说明 -->
        <div class="upload-mode-info">
          <p>当前上传模式: <strong>{{ isOssAvailable ? 'OSS直传' : '无法上传' }}</strong></p>
          <p v-if="!isOssAvailable" class="error-text">OSS服务不可用，请联系技术支持</p>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm" :loading="uploading" :disabled="!isOssAvailable">
          {{ uploading ? `上传中 ${uploadProgress}%` : '提交' }}
        </el-button>
        <el-button @click="resetForm" :disabled="uploading">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, FormInstance, UploadUserFile } from 'element-plus'
import { createFormalInterview, uploadInterviewVideoOss, uploadInterviewVideo } from '@/api/interview'
import type { FormalInterview } from '@/types/interview'
import { InterviewType } from '@/types/interview'
import { formatDate } from '@/utils/dateFormat'
import CustomDatePicker from '@/components/CustomDatePicker.vue'
import { checkOssAvailability, loadOssModule, uploadToOss } from '@/utils/ossUploader' // 导入OSS工具

// 定义事件
const emit = defineEmits(['success'])

// 表单引用
const formRef = ref<FormInstance>()
const dateValue = ref<Date | null>(null)
const fileList = ref<UploadUserFile[]>([])

// 上传状态
const uploading = ref(false)
const uploadProgress = ref(0)
const isOssAvailable = ref(false) // 默认OSS不可用
const useDirectUpload = ref(false) // 默认使用传统上传

// 表单数据
const formData = reactive({
  company: '',
  position: '',
  interviewTime: '',
  mediaFile: null as File | null
})

// 获取当前日期时间格式化字符串
const getCurrentDateTime = () => {
  const now = new Date();
  return formatDate(now.toString());
}

// 组件挂载时设置默认日期并检查OSS可用性
onMounted(async () => {
  // 设置默认日期为当前时间
  const now = new Date();
  dateValue.value = now;
  formData.interviewTime = formatDate(now.toString());
  
  // 检查OSS可用性
  try {
    // 异步加载OSS模块
    const ossAvailable = await loadOssModule();
    isOssAvailable.value = ossAvailable;
    useDirectUpload.value = ossAvailable;
    console.log('OSS可用性检查结果:', ossAvailable);
  } catch (error) {
    console.warn('OSS可用性检查失败:', error);
    isOssAvailable.value = false;
    useDirectUpload.value = false;
  }
})

// 表单验证规则
const rules = {
  company: [
    { required: true, message: '请输入面试公司', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请选择面试岗位', trigger: 'change' }
  ],
  interviewTime: [
    { required: true, message: '请选择面试时间', trigger: 'change' }
  ]
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: '今天',
    value: new Date(),
  },
  {
    text: '明天',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + 3600 * 1000 * 24)
      return date
    },
  },
  {
    text: '一周后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + 3600 * 1000 * 24 * 7)
      return date
    },
  },
]

// 处理自定义日期选择器变更
const handleCustomDateChange = (val: string) => {
  console.log('自定义日期选择器变更:', val);
  formData.interviewTime = val;
}

// 文件变更处理
const handleFileChange = (file: UploadUserFile) => {
  formData.mediaFile = file.raw || null
  fileList.value = [file]
}

// 上传视频到OSS (直传)
const uploadVideoToOss = async (file: File) => {
  try {
    if (!useDirectUpload.value || !isOssAvailable.value) {
      throw new Error('OSS直传功能不可用');
    }
    
    // 确保OSS模块已加载
    const { loadOssModule, uploadToOss } = await import('@/utils/ossUploader');
    const ossAvailable = await loadOssModule();
    
    if (!ossAvailable) {
      throw new Error('OSS模块加载失败，无法使用直传功能');
    }
    
    // 使用OSS直传
    const result = await uploadToOss(
      file,
      'interview/video',
      (percent) => {
        uploadProgress.value = percent;
      }
    );
    
    return result.url;
  } catch (error) {
    console.error('OSS直传失败:', error);
    // 不自动切换到传统上传，而是直接抛出错误
    throw new Error(`OSS直传失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 上传视频到服务器 (传统方式)
const uploadVideoToServer = async (file: File) => {
  try {
    // 准备表单数据
    const formData = new FormData();
    formData.append('file', file);
    
    // 使用传统上传方式
    const result = await uploadInterviewVideo(formData, (progressEvent: any) => {
      if (progressEvent.total) {
        uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      }
    });
    
    if (result && typeof result === 'object' && 'code' in result && result.code === 0 && result.data) {
      return result.data;
    } else {
      throw new Error((result && typeof result === 'object' && 'message' in result) ? String(result.message) : '上传失败');
    }
  } catch (error) {
    console.error('传统上传失败:', error);
    throw error;
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 创建正式面试记录
        const interviewData: Partial<FormalInterview> = {
          type: InterviewType.FORMAL,
          company: formData.company,
          position: formData.position,
          interviewTime: formData.interviewTime
        }

        const interviewId = await createFormalInterview(interviewData)

        if (!interviewId) {
          ElMessage.error('创建面试记录失败')
          return
        }

        // 如果有上传文件，则上传面试音视频
        if (formData.mediaFile) {
          uploading.value = true;
          uploadProgress.value = 0;
          
          try {
            let videoUrl = '';
            
            // 根据OSS可用性选择上传方式
            if (useDirectUpload.value && isOssAvailable.value) {
              ElMessage({
                message: '正在使用OSS直传上传视频，请稍候...',
                type: 'info',
                duration: 0
              })
              
              videoUrl = await uploadVideoToOss(formData.mediaFile);
            } else {
              // 如果OSS不可用，直接提示错误，不使用传统上传
              throw new Error('OSS直传功能不可用，请联系管理员');
            }
            
            // 关闭所有消息提示
            ElMessage.closeAll()
            
            if (!videoUrl) {
              ElMessage.warning('面试记录已创建，但音视频上传失败')
              return
            }
            
            // 更新面试记录，添加视频URL
            await updateInterviewWithVideo(interviewId, videoUrl);
            
          } catch (error) {
            // 关闭所有消息提示
            ElMessage.closeAll()
            console.error('视频上传失败:', error)
            ElMessage.error(`视频上传失败: ${error instanceof Error ? error.message : String(error)}`)
            
            // 提示用户联系管理员
            ElMessage({
              message: '上传失败，请联系管理员处理',
              type: 'warning',
              duration: 5000
            })
            
            return
          } finally {
            uploading.value = false;
            uploadProgress.value = 0;
          }
        }

        ElMessage.success('正式面试记录创建成功')
        resetForm()
        emit('success')
      } catch (error) {
        console.error('提交面试记录失败:', error)
        ElMessage.error('提交失败，请稍后重试')
        uploading.value = false;
        uploadProgress.value = 0;
      }
    } else {
      ElMessage.warning('请完善表单信息')
    }
  })
}

// 更新面试记录添加视频URL
const updateInterviewWithVideo = async (interviewId: string, videoUrl: string) => {
  try {
    // 调用API更新面试记录
    const response = await fetch(`/api/interview/${interviewId}/video`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ videoUrl })
    });
    
    const result = await response.json();
    
    if (result.code !== 0) {
      throw new Error(result.message || '更新面试视频失败');
    }
    
    return true;
  } catch (error) {
    console.error('更新面试视频失败:', error);
    throw error;
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
    fileList.value = []
    formData.mediaFile = null
  }
}
</script>

<style scoped>
.formal-interview-upload {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

h2 {
  margin-bottom: 20px;
  text-align: center;
}

.date-picker-wrapper {
  position: relative;
}

.formatted-date {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 5px;
  padding: 5px 10px;
  background-color: #ecf5ff;
  color: #409eff;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.upload-tip {
  margin-top: 8px;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.upload-tip.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #e1f3d8;
}

.upload-tip.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #faecd8;
}

.upload-tip.error {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fbc4c4;
}

.upload-tip i {
  margin-right: 5px;
}

.upload-mode-info {
  margin-top: 10px;
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-size: 12px;
}

.error-text {
  color: #f56c6c;
  font-weight: bold;
}
</style> 