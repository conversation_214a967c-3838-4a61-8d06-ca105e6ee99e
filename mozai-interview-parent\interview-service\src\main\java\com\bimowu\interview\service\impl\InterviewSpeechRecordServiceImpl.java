package com.bimowu.interview.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.interview.dao.InterviewSpeechRecordMapper;
import com.bimowu.interview.model.InterviewSpeechRecord;
import com.bimowu.interview.service.InterviewSpeechRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 面试语音转文字记录服务实现类
 */
@Service
@Slf4j
public class InterviewSpeechRecordServiceImpl extends ServiceImpl<InterviewSpeechRecordMapper, InterviewSpeechRecord>
        implements InterviewSpeechRecordService {

    @Autowired
    private InterviewSpeechRecordMapper interviewSpeechRecordMapper;

    @Override
    @Transactional
    public Long saveRecord(InterviewSpeechRecord record) {
        log.info("保存语音记录: interviewId={}, speakerId={}, startTime={}, endTime={}",
                record.getInterviewId(), record.getSpeakerId(), record.getStartTime(), record.getEndTime());
        
        // 设置时间
        Date now = new Date();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        
        interviewSpeechRecordMapper.insert(record);
        return record.getId();
    }

    @Override
    @Transactional
    public boolean batchSaveRecords(List<InterviewSpeechRecord> records) {
        log.info("批量保存语音记录: count={}", records.size());
        
        if (records == null || records.isEmpty()) {
            log.warn("语音记录列表为空，没有数据需要保存");
            return false;
        }
        
        try {
            // 设置时间，如果没有的话
            Date now = new Date();
            for (InterviewSpeechRecord record : records) {
                if (record.getCreateTime() == null) {
                    record.setCreateTime(now);
                }
                if (record.getUpdateTime() == null) {
                    record.setUpdateTime(now);
                }
            }
            
            // 使用MyBatis-Plus的批量插入方法
            boolean result = this.saveBatch(records);
            log.info("批量保存语音记录完成: {}", result ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            log.error("批量保存语音记录失败", e);
            throw new RuntimeException("批量保存语音记录失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public boolean updateRecord(InterviewSpeechRecord record) {
        log.info("更新语音记录: id={}", record.getId());
        record.setUpdateTime(new Date());
        
        int result = interviewSpeechRecordMapper.update(record);
        return result > 0;
    }

    @Override
    public InterviewSpeechRecord getRecordById(Long id) {
        log.info("获取语音记录: id={}", id);
        return interviewSpeechRecordMapper.selectById(id);
    }

    @Override
    public List<InterviewSpeechRecord> getRecordsByInterviewId(String interviewId) {
        log.info("根据面试ID获取语音记录列表: interviewId={}", interviewId);
        return interviewSpeechRecordMapper.selectByInterviewId(interviewId);
    }

    @Override
    public List<InterviewSpeechRecord> getRecordsByTimeRange(String interviewId, Integer startTime, Integer endTime) {
        log.info("根据时间范围获取语音记录列表: interviewId={}, startTime={}, endTime={}",
                interviewId, startTime, endTime);
        return interviewSpeechRecordMapper.selectByTimeRange(interviewId, startTime, endTime);
    }

    @Override
    public List<InterviewSpeechRecord> getRecordsBySpeaker(String interviewId, Integer speakerId) {
        log.info("根据发言人获取语音记录列表: interviewId={}, speakerId={}", interviewId, speakerId);
        return interviewSpeechRecordMapper.selectBySpeaker(interviewId, speakerId);
    }

    @Override
    public List<InterviewSpeechRecord> getRecordsByCondition(Map<String, Object> params) {
        log.info("根据条件获取语音记录列表: params={}", params);
        return interviewSpeechRecordMapper.selectByCondition(params);
    }

    @Override
    @Transactional
    public boolean deleteRecord(Long id) {
        log.info("删除语音记录: id={}", id);
        int result = interviewSpeechRecordMapper.deleteById(id);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteRecordsByInterviewId(String interviewId) {
        log.info("删除面试的所有语音记录: interviewId={}", interviewId);
        int result = interviewSpeechRecordMapper.deleteByInterviewId(interviewId);
        return result > 0;
    }
} 