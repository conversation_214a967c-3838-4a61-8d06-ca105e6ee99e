#!/bin/bash

echo "🐧 Linux Node.js 16 构建修复脚本"
echo "当前 Node.js 版本: $(node --version)"

# 检查 Node.js 版本
NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ 需要 Node.js 16 或更高版本"
    exit 1
fi

echo "✅ Node.js 版本检查通过"

# 1. 清理环境
echo "🧹 清理构建环境..."
rm -rf node_modules package-lock.json dist-interview-ai .vite

# 2. 清理 npm 缓存
echo "🗑️ 清理 npm 缓存..."
npm cache clean --force

# 3. 安装兼容版本的依赖
echo "📦 安装 Node.js 16 兼容依赖..."
npm install

# 4. 设置环境变量
export NODE_OPTIONS="--max-old-space-size=4096"
export SKIP_OPTIONAL_DEPENDENCIES="true"

# 5. 尝试构建
echo "🏗️ 开始构建..."
npm run build:node16

# 6. 检查构建结果
if [ -d "dist-interview-ai" ] && [ "$(ls -A dist-interview-ai)" ]; then
    echo "✅ 构建成功！"
    echo "📁 构建产物大小:"
    du -sh dist-interview-ai
    echo "📋 构建内容:"
    ls -la dist-interview-ai/
    echo ""
    echo "🚀 可以部署了！"
else
    echo "❌ 构建失败，尝试备用方案..."
    
    # 备用方案：使用原始配置但跳过可选依赖
    echo "🔄 尝试备用构建方案..."
    SKIP_OPTIONAL_DEPENDENCIES=true npm run build
    
    if [ -d "dist-interview-ai" ] && [ "$(ls -A dist-interview-ai)" ]; then
        echo "✅ 备用方案构建成功！"
    else
        echo "❌ 所有构建方案都失败了"
        echo ""
        echo "💡 建议解决方案:"
        echo "1. 升级到 Node.js 18+: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs"
        echo "2. 使用 yarn: rm -rf node_modules && yarn install && yarn build"
        echo "3. 检查系统依赖: sudo apt-get update && sudo apt-get install -y build-essential"
        echo "4. 手动安装 Python: sudo apt-get install -y python3 python3-pip"
        exit 1
    fi
fi
