# Linux 服务器部署指南

## 🎯 一键部署命令

在 Linux 服务器上执行以下命令：

```bash
# 1. 进入项目目录
cd /opt/code/mozai-interview-front

# 2. 拉取最新代码
git pull

# 3. 给脚本执行权限
chmod +x fix-linux-build.sh

# 4. 运行一键修复构建脚本
./fix-linux-build.sh
```

## 🔧 如果一键脚本失败

### 手动步骤：

```bash
# 1. 清理环境
rm -rf node_modules package-lock.json dist-interview-ai .vite
npm cache clean --force

# 2. 安装依赖
npm install

# 3. 设置环境变量并构建
export NODE_OPTIONS="--max-old-space-size=4096"
export SKIP_OPTIONAL_DEPENDENCIES="true"
npm run build:node16

# 4. 检查构建结果
ls -la dist-interview-ai/
```

## 🚨 常见问题解决

### 问题1: Node.js 版本过低
```bash
# 检查版本
node --version

# 如果低于 16，升级 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 问题2: 缺少构建工具
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y build-essential python3 python3-pip

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y python3 python3-pip
```

### 问题3: npm 权限问题
```bash
# 修复 npm 权限
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules
```

### 问题4: 内存不足
```bash
# 增加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## 📋 验证部署成功

构建成功后应该看到：
```
✅ 构建成功！
📁 构建产物大小: 2.1M dist-interview-ai
📋 构建内容:
drwxr-xr-x assets/
-rw-r--r-- index.html
🚀 可以部署了！
```

## 🌐 配置 Web 服务器

### Nginx 配置示例：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /opt/code/mozai-interview-front/dist-interview-ai;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /assets {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### Apache 配置示例：
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /opt/code/mozai-interview-front/dist-interview-ai
    
    <Directory "/opt/code/mozai-interview-front/dist-interview-ai">
        AllowOverride All
        Require all granted
        
        # SPA 路由支持
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
</VirtualHost>
```

## 🔄 自动化部署脚本

创建 `/opt/code/deploy.sh`：
```bash
#!/bin/bash
cd /opt/code/mozai-interview-front
git pull
./fix-linux-build.sh
sudo systemctl reload nginx
echo "部署完成！"
```

## 📞 故障排除

如果所有方法都失败：

1. **检查系统信息**：
```bash
uname -a
node --version
npm --version
cat /etc/os-release
```

2. **查看详细错误**：
```bash
npm run build:node16 --verbose
```

3. **使用 yarn 替代**：
```bash
npm install -g yarn
rm -rf node_modules package-lock.json
yarn install
yarn build
```

4. **联系支持**：提供以上信息和完整错误日志

---

**重要提示**：这个配置专门为 Node.js 16 + Linux 环境优化，确保最大兼容性。
