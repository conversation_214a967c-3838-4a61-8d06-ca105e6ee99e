const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Node.js 16 兼容性构建脚本');
console.log('Node.js 版本:', process.version);

// 检查 Node.js 版本
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 16) {
  console.error('❌ 需要 Node.js 16 或更高版本');
  process.exit(1);
}

try {
  // 1. 清理构建目录
  console.log('🧹 清理构建目录...');
  if (fs.existsSync('dist-interview-ai')) {
    fs.rmSync('dist-interview-ai', { recursive: true, force: true });
  }
  
  // 2. 设置环境变量
  process.env.NODE_OPTIONS = '--max-old-space-size=4096';
  process.env.VITE_LEGACY_BUILD = 'true';
  
  // 3. 执行构建
  console.log('🏗️ 开始构建...');
  execSync('npm run build', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_OPTIONS: '--max-old-space-size=4096'
    }
  });
  
  console.log('✅ 构建成功完成！');
  
} catch (error) {
  console.error('❌ 构建失败:', error.message);
  
  // 提供解决方案建议
  console.log('\n💡 解决方案建议:');
  console.log('1. 确保已运行: npm install');
  console.log('2. 清理缓存: rm -rf node_modules package-lock.json && npm install');
  console.log('3. 升级到 Node.js 18+ 版本');
  console.log('4. 使用 build:safe 脚本: npm run build:safe');
  
  process.exit(1);
}
