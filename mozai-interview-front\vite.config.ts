import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 检查环境变量是否跳过可选依赖
const skipOptionalDeps = process.env.SKIP_OPTIONAL_DEPENDENCIES === 'true'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  base: './',
  build: {
    outDir: 'dist-interview-ai',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    // 确保生成静态资源的文件名包含哈希值
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            // 将ali-oss单独打包
            if (id.includes('ali-oss')) {
              return 'ali-oss';
            }
            return 'vendor';
          }
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      },
      // 如果设置了跳过可选依赖，则外部化ali-oss
      external: skipOptionalDeps ? ['ali-oss'] : []
    },
    // 减少构建警告
    chunkSizeWarningLimit: 1000,
    // 优化大型依赖项
    commonjsOptions: {
      include: [/node_modules/],
      transformMixedEsModules: true
    }
  },
  server: {
    port: 3000,
    open: true,
    cors: true
  },
  // 优化依赖预构建
  optimizeDeps: {
    exclude: ['ali-oss'] // 排除ali-oss预构建
  }
}) 