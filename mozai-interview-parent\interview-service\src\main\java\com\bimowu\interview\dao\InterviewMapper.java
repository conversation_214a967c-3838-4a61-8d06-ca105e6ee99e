package com.bimowu.interview.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bimowu.interview.model.Interview;
import org.mapstruct.Mapper;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * 面试信息Mapper接口
 */
@Mapper
public interface InterviewMapper extends BaseMapper<Interview> {

    /**
     * 插入面试信息
     * 
     * @param interview 面试信息
     * @return 影响的行数
     */
    int insert(Interview interview);
    
    /**
     * 更新面试信息
     * 
     * @param interview 面试信息
     * @return 影响的行数
     */
    int update(Interview interview);
    
    /**
     * 根据ID查询面试信息
     * 
     * @param id 面试ID
     * @return 面试信息
     */
    Interview selectById(@Param("id") String id);
    
    /**
     * 根据条件查询面试信息列表
     * 
     * @param params 查询条件
     * @return 面试信息列表
     */
    List<Interview> selectByCondition(Map<String, Object> params);
    
    /**
     * 根据类型查询面试信息列表
     * 
     * @param type 面试类型
     * @return 面试信息列表
     */
    List<Interview> selectByType(@Param("type") String type);
    
    /**
     * 更新面试状态
     * 
     * @param id 面试ID
     * @param status 面试状态
     * @return 影响的行数
     */
    int updateStatus(@Param("id") String id, @Param("status") Integer status);
    
    /**
     * 更新面试结果
     * 
     * @param id 面试ID
     * @param overallScore 总体评分
     * @param feedback 面试反馈
     * @param strengths 优势表现
     * @param improvements 改进建议
     * @return 影响的行数
     */
    int updateResult(@Param("id") String id, @Param("overallScore") Integer overallScore,
                     @Param("feedback") String feedback, @Param("strengths") String strengths,
                     @Param("improvements") String improvements);
    
    /**
     * 更新面试通过/未通过状态
     * 
     * @param id 面试ID
     * @param result 面试结果(0:未通过,1:通过)
     * @return 影响的行数
     */
    int updateInterviewResult(@Param("id") String id, @Param("result") Integer result);
    
    /**
     * 更新面试视频URL
     * 
     * @param id 面试ID
     * @param videoUrl 视频URL
     * @return 影响的行数
     */
    int updateVideoUrl(@Param("id") String id, @Param("videoUrl") String videoUrl);
    
    /**
     * 根据ID删除面试信息
     * 
     * @param id 面试ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") String id);
} 