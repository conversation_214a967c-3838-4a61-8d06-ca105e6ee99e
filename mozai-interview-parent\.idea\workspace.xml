<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8c64f1a6-764e-4e88-9ccc-afb8269c4f90" name="Changes" comment="生产环境" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Pull.Settings">
    <option name="OPTIONS">
      <set>
        <option value="REBASE" />
      </set>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="D:\apache-maven-3.2.3-bin\apache-maven-3.2.3-bin\apache-maven-3.2.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2yUbotdqBsJ0qhoyeBZWHA6o7Bs" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.TranscriptionServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;Maven.mozai-interview-parent [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.mozai-interview-parent [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.InterviewWebApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/IdeaProjects/sso/interview/mozai-interview-parent&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Problems&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.6448276&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;redis&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\sso\interview\mozai-interview-parent\interview-web\src\main\resources\mappers" />
      <recent name="C:\Users\<USER>\IdeaProjects\sso\interview\mozai-interview-parent\interview-service\src\main\java\com\bimowu\interview\model" />
      <recent name="C:\Users\<USER>\IdeaProjects\mozai-interview-parent\interview-web\src\main\java\com\bimowu" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.bimowu.interview.dao" />
      <recent name="com.bimowu.interview.controller" />
      <recent name="com.bimowu.filter" />
      <recent name="com.bimowu.interview.utils" />
    </key>
  </component>
  <component name="RunManager" selected="Application.TranscriptionServiceImpl">
    <configuration name="TranscriptionServiceImpl" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.bimowu.interview.service.impl.TranscriptionServiceImpl" />
      <module name="interview-service" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bimowu.interview.service.impl.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="InterviewWebApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="interview-web" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bimowu.InterviewWebApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.TranscriptionServiceImpl" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8c64f1a6-764e-4e88-9ccc-afb8269c4f90" name="Changes" comment="" />
      <created>1749892276118</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749892276118</updated>
      <workItem from="1749892277276" duration="1194000" />
      <workItem from="1750387368853" duration="6528000" />
      <workItem from="1750406475892" duration="2512000" />
      <workItem from="1751522547058" duration="4322000" />
      <workItem from="1751527538524" duration="8512000" />
      <workItem from="1751594176824" duration="13886000" />
      <workItem from="1751853274662" duration="217000" />
      <workItem from="1751854197571" duration="16000" />
      <workItem from="1751960011044" duration="3866000" />
      <workItem from="1751974126916" duration="495000" />
      <workItem from="1752026145436" duration="57655000" />
      <workItem from="1752206006027" duration="11704000" />
      <workItem from="1752281988556" duration="1493000" />
      <workItem from="1752466487491" duration="2138000" />
      <workItem from="1752561858627" duration="4458000" />
      <workItem from="1752578323326" duration="23194000" />
      <workItem from="1753184246277" duration="553000" />
      <workItem from="1753237140067" duration="2153000" />
      <workItem from="1755074825208" duration="607000" />
      <workItem from="1755136664956" duration="1477000" />
      <workItem from="1755484126338" duration="1323000" />
    </task>
    <task id="LOCAL-00035" summary="oss迁移到前端--------oss角色---修改bucketName">
      <option name="closed" value="true" />
      <created>1752133481155</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1752133481155</updated>
    </task>
    <task id="LOCAL-00036" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752136042978</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1752136042978</updated>
    </task>
    <task id="LOCAL-00037" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752136424552</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1752136424552</updated>
    </task>
    <task id="LOCAL-00038" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752137078602</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1752137078602</updated>
    </task>
    <task id="LOCAL-00039" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752138056655</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1752138056655</updated>
    </task>
    <task id="LOCAL-00040" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752138304619</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1752138304619</updated>
    </task>
    <task id="LOCAL-00041" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752139396433</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1752139396433</updated>
    </task>
    <task id="LOCAL-00042" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752141995171</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1752141995171</updated>
    </task>
    <task id="LOCAL-00043" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752142604339</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1752142604339</updated>
    </task>
    <task id="LOCAL-00044" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752142687300</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1752142687300</updated>
    </task>
    <task id="LOCAL-00045" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752142720073</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1752142720073</updated>
    </task>
    <task id="LOCAL-00046" summary="ai问题优化">
      <option name="closed" value="true" />
      <created>1752143135068</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1752143135068</updated>
    </task>
    <task id="LOCAL-00047" summary="oss">
      <option name="closed" value="true" />
      <created>1752143288980</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1752143288980</updated>
    </task>
    <task id="LOCAL-00048" summary="oss">
      <option name="closed" value="true" />
      <created>1752143710241</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1752143710241</updated>
    </task>
    <task id="LOCAL-00049" summary="oss">
      <option name="closed" value="true" />
      <created>1752143950128</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1752143950128</updated>
    </task>
    <task id="LOCAL-00050" summary="面试解析">
      <option name="closed" value="true" />
      <created>1752207768168</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1752207768168</updated>
    </task>
    <task id="LOCAL-00051" summary="面试解析">
      <option name="closed" value="true" />
      <created>1752219016300</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1752219016300</updated>
    </task>
    <task id="LOCAL-00052" summary="面试解析">
      <option name="closed" value="true" />
      <created>1752219702564</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1752219702564</updated>
    </task>
    <task id="LOCAL-00053" summary="面试解析-异常处理">
      <option name="closed" value="true" />
      <created>1752222982237</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1752222982237</updated>
    </task>
    <task id="LOCAL-00054" summary="面试解析-异常处理">
      <option name="closed" value="true" />
      <created>1752223485516</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1752223485516</updated>
    </task>
    <task id="LOCAL-00055" summary="面试解析-异常处理">
      <option name="closed" value="true" />
      <created>1752223572947</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1752223572947</updated>
    </task>
    <task id="LOCAL-00056" summary="修改重试次数">
      <option name="closed" value="true" />
      <created>1752225518145</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1752225518145</updated>
    </task>
    <task id="LOCAL-00057" summary="任务相关表代码">
      <option name="closed" value="true" />
      <created>1752467441745</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1752467441745</updated>
    </task>
    <task id="LOCAL-00058" summary="待办相关表代码">
      <option name="closed" value="true" />
      <created>1752562800101</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1752562800101</updated>
    </task>
    <task id="LOCAL-00059" summary="面试结果字段">
      <option name="closed" value="true" />
      <created>1752564260377</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1752564260377</updated>
    </task>
    <task id="LOCAL-00060" summary="bug">
      <option name="closed" value="true" />
      <created>1752564326240</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1752564326240</updated>
    </task>
    <task id="LOCAL-00061" summary="待办、进度添加resume_id">
      <option name="closed" value="true" />
      <created>1752630043278</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1752630043278</updated>
    </task>
    <task id="LOCAL-00062" summary="面试前判断进度">
      <option name="closed" value="true" />
      <created>1752630792141</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1752630792141</updated>
    </task>
    <task id="LOCAL-00063" summary="面试完成时，更新待办，根据resumeId修改">
      <option name="closed" value="true" />
      <created>1752631295861</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1752631295861</updated>
    </task>
    <task id="LOCAL-00064" summary="正式面试上传不受进度控制">
      <option name="closed" value="true" />
      <created>1752655723704</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1752655723704</updated>
    </task>
    <task id="LOCAL-00065" summary="oss修改">
      <option name="closed" value="true" />
      <created>1752656621659</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1752656621659</updated>
    </task>
    <task id="LOCAL-00066" summary="bug修复">
      <option name="closed" value="true" />
      <created>1752657390119</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1752657390119</updated>
    </task>
    <task id="LOCAL-00067" summary="设置通义听悟appkey">
      <option name="closed" value="true" />
      <created>1752660133044</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1752660133044</updated>
    </task>
    <task id="LOCAL-00068" summary="设置通义听悟key">
      <option name="closed" value="true" />
      <created>1752660589547</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1752660589547</updated>
    </task>
    <task id="LOCAL-00069" summary="设置通义听悟key">
      <option name="closed" value="true" />
      <created>1752660937639</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1752660937639</updated>
    </task>
    <task id="LOCAL-00070" summary="设置通义听悟key">
      <option name="closed" value="true" />
      <created>1752718933686</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1752718933686</updated>
    </task>
    <task id="LOCAL-00071" summary="设置通义听悟key">
      <option name="closed" value="true" />
      <created>1752719102767</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1752719102768</updated>
    </task>
    <task id="LOCAL-00072" summary="面试添加校验，禁止重复面试">
      <option name="closed" value="true" />
      <created>1752724479974</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1752724479974</updated>
    </task>
    <task id="LOCAL-00073" summary="面试添加校验，禁止重复面试">
      <option name="closed" value="true" />
      <created>1752724941951</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1752724941951</updated>
    </task>
    <task id="LOCAL-00074" summary="面试添加校验，禁止重复面试">
      <option name="closed" value="true" />
      <created>1752725244181</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1752725244182</updated>
    </task>
    <task id="LOCAL-00075" summary="模拟面试不需要章节问答转写">
      <option name="closed" value="true" />
      <created>1752730901181</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1752730901181</updated>
    </task>
    <task id="LOCAL-00076" summary="ai分析完成需要人工确认">
      <option name="closed" value="true" />
      <created>1752732117176</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1752732117176</updated>
    </task>
    <task id="LOCAL-00077" summary="通义听悟appKey">
      <option name="closed" value="true" />
      <created>1752746515512</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1752746515512</updated>
    </task>
    <task id="LOCAL-00078" summary="通义听悟appKey">
      <option name="closed" value="true" />
      <created>1752747255640</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1752747255640</updated>
    </task>
    <task id="LOCAL-00079" summary="重试次数">
      <option name="closed" value="true" />
      <created>1752749274367</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1752749274367</updated>
    </task>
    <task id="LOCAL-00080" summary="日志">
      <option name="closed" value="true" />
      <created>1753184410608</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1753184410608</updated>
    </task>
    <task id="LOCAL-00081" summary="bug修复">
      <option name="closed" value="true" />
      <created>1753184596015</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1753184596015</updated>
    </task>
    <task id="LOCAL-00082" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755139036719</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1755139036719</updated>
    </task>
    <task id="LOCAL-00083" summary="生产环境">
      <option name="closed" value="true" />
      <created>1755484195214</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1755484195214</updated>
    </task>
    <option name="localTasksCounter" value="84" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="d151a69e-9660-40ea-a03b-375f45cfcb79" value="TOOL_WINDOW" />
        <entry key="358b24a6-5556-4fd3-9c4d-626466a63b53" value="TOOL_WINDOW" />
        <entry key="1deb3007-5187-4077-a2e4-3c00b2eb0eb5" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="1deb3007-5187-4077-a2e4-3c00b2eb0eb5">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="358b24a6-5556-4fd3-9c4d-626466a63b53">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="d151a69e-9660-40ea-a03b-375f45cfcb79">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="oss迁移到前端--------oss角色---修改bucketName" />
    <MESSAGE value="ai问题优化" />
    <MESSAGE value="oss" />
    <MESSAGE value="面试解析" />
    <MESSAGE value="面试解析-异常处理" />
    <MESSAGE value="修改重试次数" />
    <MESSAGE value="任务相关表代码" />
    <MESSAGE value="待办相关表代码" />
    <MESSAGE value="面试结果字段" />
    <MESSAGE value="bug" />
    <MESSAGE value="待办、进度添加resume_id" />
    <MESSAGE value="面试前判断进度" />
    <MESSAGE value="面试完成时，更新待办，根据resumeId修改" />
    <MESSAGE value="正式面试上传不受进度控制" />
    <MESSAGE value="oss修改" />
    <MESSAGE value="设置通义听悟appkey" />
    <MESSAGE value="设置通义听悟key" />
    <MESSAGE value="面试添加校验，禁止重复面试" />
    <MESSAGE value="模拟面试不需要章节问答转写" />
    <MESSAGE value="ai分析完成需要人工确认" />
    <MESSAGE value="通义听悟appKey" />
    <MESSAGE value="重试次数" />
    <MESSAGE value="日志" />
    <MESSAGE value="bug修复" />
    <MESSAGE value="生产环境" />
    <option name="LAST_COMMIT_MESSAGE" value="生产环境" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/interview-service/src/main/java/com/bimowu/interview/service/InterviewTranscriptionService.java</url>
          <line>13</line>
          <properties class="com.bimowu.interview.service.InterviewTranscriptionService">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/interview-service/src/main/java/com/bimowu/interview/service/TranscriptionService.java</url>
          <line>33</line>
          <properties class="com.bimowu.interview.service.TranscriptionService" method="getInterviewTranscriptions">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>jar://$MAVEN_REPOSITORY$/org/springframework/spring-web/5.1.5.RELEASE/spring-web-5.1.5.RELEASE.jar!/org/springframework/web/multipart/MultipartFile.class</url>
          <line>32</line>
          <properties class="org.springframework.web.multipart.MultipartFile" method="getInputStream">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/interview-service/src/main/java/com/bimowu/interview/service/InterviewTranscriptionService.java</url>
          <line>23</line>
          <properties class="com.bimowu.interview.service.InterviewTranscriptionService" method="saveTranscription">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/interview-service/src/main/java/com/bimowu/interview/service/InterviewService.java</url>
          <line>19</line>
          <properties class="com.bimowu.interview.service.InterviewService" method="createInterview">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/interview-service/src/main/java/com/bimowu/interview/service/impl/TranscriptionServiceImpl.java</url>
          <line>206</line>
          <properties class="com.bimowu.interview.service.impl.TranscriptionServiceImpl" method="getTaskInfo">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>