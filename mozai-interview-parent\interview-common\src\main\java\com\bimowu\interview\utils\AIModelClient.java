package com.bimowu.interview.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI模型客户端
 * 用于调用大模型API生成面试问题
 */
@Component
public class AIModelClient {

    @Value("${ai.model.url:https://api.example.com/v1/completions}")
    private String apiUrl;
    
    @Value("${ai.model.key:}")
    private String apiKey;
    
    @Value("${ai.model.type:gpt-3.5-turbo}")
    private String modelType;
    
    /**
     * 生成面试问题
     * @param resumeText 简历文本
     * @return 生成的面试问题列表
     * @throws Exception 调用过程中可能的异常
     */
    public List<String> generateInterviewQuestions(String resumeText) throws Exception {
        // 构建请求正文
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", modelType);
        
        List<Map<String, String>> messages = new ArrayList<>();
        
        // 系统指令
        Map<String, String> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", "你是一个专业java面试官，根据用户提供的简历内容，生成至少20个专业、有针对性的面试问题。" +
                "根据简历中的个人的工作经历、实习经历、项目经验、练手项目、专业技能、技能特长内容进行构建面试问题" +
                "返回格式为json数组，每个问题单独作为数组的一个元素，如 [\"问题1\", \"问题2\", ...]");
        
        // 用户简历内容
        Map<String, String> userMessage = new HashMap<>();
        userMessage.put("role", "user");
        userMessage.put("content", "这是我的简历内容，请生成面试问题：\n" + resumeText);
        
        messages.add(systemMessage);
        messages.add(userMessage);
        
        requestBody.put("messages", messages);
        requestBody.put("temperature", 0.7);
        requestBody.put("max_tokens", 2000);
        
        // 发送请求
        String jsonResponse = sendRequest(requestBody);
        
        // 解析响应
        return parseResponse(jsonResponse);
    }
    
    /**
     * 发送请求到AI模型API
     * @param requestBody 请求参数
     * @return 响应内容
     * @throws Exception 请求过程中可能的异常
     */
    private String sendRequest(Map<String, Object> requestBody) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(apiUrl);
            
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");
            if (apiKey != null && !apiKey.isEmpty()) {
                httpPost.setHeader("Authorization", "Bearer " + apiKey);
            }
            
            // 设置请求体
            String jsonBody = JSON.toJSONString(requestBody);
            StringEntity entity = new StringEntity(jsonBody, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            
            // 发送请求并获取响应
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                if (responseEntity != null) {
                    String responseString = EntityUtils.toString(responseEntity);
                    return responseString;
                } else {
                    throw new Exception("API响应为空");
                }
            }
        }
    }
    
    /**
     * 解析API响应内容，提取生成的问题
     * @param jsonResponse API响应内容
     * @return 提取的问题列表
     */
    private List<String> parseResponse(String jsonResponse) {
        List<String> questions = new ArrayList<>();
        
        try {
            JSONObject responseObj = JSON.parseObject(jsonResponse);
            
            // 尝试从标准GPT格式中提取
            if (responseObj.containsKey("choices") && responseObj.getJSONArray("choices").size() > 0) {
                String content = responseObj.getJSONArray("choices")
                        .getJSONObject(0)
                        .getJSONObject("message")
                        .getString("content");
                
                // 尝试解析JSON数组
                try {
                    List<String> parsedQuestions = JSON.parseArray(content, String.class);
                    questions.addAll(parsedQuestions);
                } catch (Exception e) {
                    // 不是JSON格式，按行拆分
                    String[] lines = content.split("\\n");
                    for (String line : lines) {
                        // 去除序号和空行
                        line = line.trim();
                        if (!line.isEmpty()) {
                            // 移除数字序号和点（例如：1. 或 1）
                            line = line.replaceAll("^\\d+[.、)\\s]+", "");
                            questions.add(line);
                        }
                    }
                }
            }
            
            // 如果未能提取到足够的问题，添加一些默认问题
            if (questions.size() < 5) {
                questions.add("请介绍一下您自己和您的技术背景。");
                questions.add("您认为自己最擅长的技术是什么？为什么？");
                questions.add("描述一个您曾经解决的复杂技术问题。");
                questions.add("您是如何保持技术更新和学习的？");
                questions.add("您对团队协作有什么看法？");
                questions.add("您期望在新的工作环境中获得什么？");
                questions.add("您如何处理工作中的压力和挑战？");
                questions.add("您最近阅读了哪些与您专业相关的书籍或文章？");
                questions.add("您的职业规划是什么？");
                questions.add("您为什么对我们公司感兴趣？");
            }
            
            // 确保至少返回20个问题
            while (questions.size() < 20) {
                int index = (int) (Math.random() * 10);
                String question = "补充问题" + (questions.size() + 1) + ": " + questions.get(index);
                questions.add(question);
            }
            
        } catch (Exception e) {
            // 解析失败时返回一些默认问题
            questions.add("请介绍一下您自己和您的教育背景。");
            questions.add("您为什么选择这个行业？");
            questions.add("您有哪些专业技能和资格认证？");
            questions.add("您认为自己最大的优势是什么？");
            questions.add("您如何看待团队合作？");
            questions.add("您曾经参与过哪些项目？您在其中担任什么角色？");
            questions.add("您如何处理工作压力？");
            questions.add("您的职业目标是什么？");
            questions.add("您对公司文化有什么期望？");
            questions.add("您有什么问题想问我们吗？");
            questions.add("您曾经解决过哪些复杂的技术难题？");
            questions.add("您如何保持技术更新和学习？");
            questions.add("您如何平衡工作与生活？");
            questions.add("您对加班有什么看法？");
            questions.add("您能描述一下您的工作风格吗？");
            questions.add("您期望的薪资范围是多少？");
            questions.add("您希望在新工作中获得什么样的支持和发展机会？");
            questions.add("您的优势和劣势是什么？");
            questions.add("您为什么离开上一份工作？");
            questions.add("您希望在5年后达到什么样的职业发展？");
        }
        
        return questions;
    }
} 