package com.bimowu.interview.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 面试语音转文字章节段落记录实体类
 */
@Data
public class InterviewSpeechChapter {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 面试ID
     */
    private String interviewId;
    
    /**
     * 开始时间(秒)
     */
    private Long startTime;
    
    /**
     * 结束时间(秒)
     */
    private Long endTime;
    
    /**
     * 章节标题
     */
    private String chapterTitle;
    
    /**
     * 章节总结
     */
    private String chapterSummary;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 