package com.bimowu.interview.model;


import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 项目问题表
 * @TableName resume_project_question
 */
@TableName(value ="resume_project_question")
@Data
public class ResumeProjectQuestion implements Serializable {
    /**
     * 
     */
    @TableId
    private Long queId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 
     */
    private Long conId;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 答案
     */
    private String answer;

    /**
     * 问题顺序
     */
    private Integer questionOrder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}