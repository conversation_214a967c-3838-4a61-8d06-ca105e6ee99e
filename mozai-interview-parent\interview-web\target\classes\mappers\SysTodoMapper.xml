<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.SysTodoMapper">
    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.SysTodo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="title" jdbcType="VARCHAR" property="title" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="todo_type" jdbcType="INTEGER" property="todoType" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, title, content, url, todo_type, status, create_time, update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_todo
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectUnfinishedByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_todo
        where user_id = #{userId,jdbcType=BIGINT}
        and status = 0
        order by create_time desc
    </select>

    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sys_todo
        where user_id = #{userId,jdbcType=BIGINT}
        order by create_time desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from sys_todo
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.bimowu.interview.model.SysTodo">
        insert into sys_todo (user_id, title, content, url, todo_type, status)
        values (
            #{userId,jdbcType=BIGINT},
            #{title,jdbcType=VARCHAR},
            #{content,jdbcType=VARCHAR},
            #{url,jdbcType=VARCHAR},
            #{todoType,jdbcType=INTEGER},
            #{status,jdbcType=INTEGER}
        )
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.bimowu.interview.model.SysTodo">
        update sys_todo
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="todoType != null">
                todo_type = #{todoType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper> 