@echo off
echo 🚀 超级修复脚本 - 解决 Element Plus 模块问题
echo Node.js 版本:
node --version

echo.
echo 🔄 完全清理环境...
echo ----------------------------------------
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist dist-interview-ai rmdir /s /q dist-interview-ai
if exist .vite rmdir /s /q .vite
npm cache clean --force

echo.
echo 🔄 安装依赖...
echo ----------------------------------------
npm install

echo.
echo 🔄 安装 Element Plus 2.0.6...
echo ----------------------------------------
npm install element-plus@2.0.6 --save-exact

echo.
echo 🔄 修复 Element Plus 模块引用...
echo ----------------------------------------
npm run fix:modules

echo.
echo 🔄 开始构建...
echo ----------------------------------------
npm run build:final

echo.
if exist dist-interview-ai (
    echo 🎉 构建成功完成！
    echo 📁 构建产物:
    dir dist-interview-ai
    echo.
    echo ✅ 可以部署了！
) else (
    echo ❌ 构建仍然失败
    echo.
    echo 🔧 手动尝试步骤:
    echo 1. npm run fix:element
    echo 2. npm run fix:modules
    echo 3. npx vite build --config vite.config.bypass.ts
    echo.
    echo 💡 或者考虑:
    echo - 升级到 Node.js 18+
    echo - 使用 yarn 替代 npm
    echo - 联系开发团队
)

pause
