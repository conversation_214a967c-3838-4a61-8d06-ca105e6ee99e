{"name": "quiz-ai-interview", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "download-models": "node scripts/download-models.js", "build:full": "npm run download-models && npm run build", "build:linux": "SKIP_OPTIONAL_DEPENDENCIES=true npm install --no-optional && npm run build", "build:safe": "node scripts/build-for-linux.js"}, "dependencies": {"@mediapipe/camera_utils": "^0.3.1", "@mediapipe/face_detection": "^0.4.1", "@types/uuid": "^10.0.0", "axios": "^1.5.0", "element-plus": "^2.3.12", "face-api.js": "^0.20.0", "pinia": "^2.1.6", "quiz-ai-interview": "file:", "recordrtc": "^5.6.2", "three": "^0.160.1", "uuid": "^11.1.0", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@types/node": "^20.6.0", "@types/recordrtc": "^5.6.14", "@types/three": "^0.160.0", "@vitejs/plugin-vue": "^5.2.4", "sass": "^1.66.1", "typescript": "^5.2.2", "vite": "^6.3.5"}, "optionalDependencies": {"ali-oss": "^6.23.0", "@types/ali-oss": "^6.16.11"}}