package com.bimowu.interview.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bimowu.interview.model.InterviewTranscription;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 面试转写记录服务接口
 */
public interface InterviewTranscriptionService extends IService<InterviewTranscription> {
    
    /**
     * 保存面试问题的转写结果
     * 
     * @param audioFile 音频文件
     * @param questionIndex 问题索引
     * @param interviewId 面试ID
     * @param question 问题内容
     * @return 转写的文本
     */
    String saveTranscription(MultipartFile audioFile, Integer questionIndex, String interviewId, String question);
    
    /**
     * 批量保存转写记录
     *
     * @param transcriptions 转写记录列表
     * @return 是否成功
     */
    boolean batchSaveTranscriptions(List<InterviewTranscription> transcriptions);
    
    /**
     * 获取面试的所有转写结果
     * 
     * @param interviewId 面试ID
     * @return 问题索引到转写文本的映射
     */
    Map<Integer, String> getInterviewTranscriptions(String interviewId);
    
    /**
     * 获取面试的所有转写记录
     * 
     * @param interviewId 面试ID
     * @return 转写记录列表
     */
    List<InterviewTranscription> getTranscriptionList(String interviewId);
    
    /**
     * 获取面试问题的转写记录
     * 
     * @param interviewId 面试ID
     * @param questionIndex 问题索引
     * @return 转写记录
     */
    InterviewTranscription getTranscription(String interviewId, Integer questionIndex);
    
    /**
     * 更新转写记录
     * 
     * @param transcription 转写记录
     * @return 是否成功
     */
    boolean updateTranscription(InterviewTranscription transcription);
    
    /**
     * 删除转写记录
     * 
     * @param id 记录ID
     * @return 是否成功
     */
    boolean deleteTranscription(Long id);
    
    /**
     * 删除面试的所有转写记录
     * 
     * @param interviewId 面试ID
     * @return 是否成功
     */
    boolean deleteTranscriptionsByInterviewId(String interviewId);
} 