package com.bimowu.interview.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChoice;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * AI 调用工具类
 */
@Service
public class AiManager {

    // AI 调用客服端
    @Autowired
    private ArkService arkService;
    @Value("${ai.model-name}")
    private String modelName;
     String SYSTEM_PROMPT = "你是一位资深面试官，擅长综合评估候选人的技术能力和软技能。我将提供整场面试的题目列表、" +
            "候选人回答列表和标准答案列表，如果我没有提供标准答案，请你自己联网查询该问题的参考答案。请你：回答整场面试反馈、优势表现、改进建议，并且只给出整场的评分（0-100分）" +
            "回答格式如下（以下4点请用 | 隔开）： 面试反馈：（大约50-100字）|评分： 94 |优势表现：xx |改进建议：xx";

    /**
     * 根据项目经验生成面试问题
     * 
     * @param projectDescription 项目经验描述
     * @param position 面试岗位
     * @param count 需要生成的问题数量
     * @return 生成的面试问题列表
     */
    public List<String> generateInterviewQuestions(String projectDescription, String position, Integer count) {
        final List<ChatMessage> messages = new ArrayList<>();
        
        // 系统提示词，引导AI生成高质量的面试问题
        final String questionGenerationPrompt = 
                "你是一位资深技术面试官，需要根据候选人的项目经验生成专业的技术面试问题。" +
                "请基于候选人的项目描述，生成" + count + "个针对" + position + "岗位的高质量面试问题。" +
                "问题应该：\n" +
                "1. 与项目技术栈和背景相关\n" +
                "2. 能够考察候选人的专业能力和项目理解\n" +
                "3. 包含一定的深度，能够区分候选人的技术水平\n" +
                "4. 注重实际问题解决能力的考察\n" +
                "5. 难度不要太高，面向大四在校生\n" +
                "请直接返回问题列表，每个问题一行，不要有额外的前缀或解释。";
        
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(questionGenerationPrompt)
                .build();
        messages.add(systemMessage);
        
        // 用户消息，提供项目经验描述
        final ChatMessage userMessage = ChatMessage.builder()
                .role(ChatMessageRole.USER)
                .content("候选人项目经验：" + projectDescription)
                .build();
        messages.add(userMessage);
        
        // 调用AI生成问题
        String response = doChat(messages);
        
        // 解析返回的问题列表
        List<String> questions = new ArrayList<>();
        String[] lines = response.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (!line.isEmpty()) {
                // 去除可能的序号前缀
                line = line.replaceAll("^\\d+\\.\\s*", "").trim();
                questions.add(line);
            }
        }
        
        // 确保问题数量不超过请求的数量
        if (questions.size() > count) {
            questions = questions.subList(0, count);
        }
        
        return questions;
    }

    /**
     * 聊天（只允许传入系统预设和用户预设）
     *
     * @return
     */
    public String doChat(List<String> question, List<String> transcription, List<String> answer) {
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage systemMessage = ChatMessage.builder()
                .role(ChatMessageRole.SYSTEM)
                .content(SYSTEM_PROMPT)
                .build();
        messages.add(systemMessage);
        for(int i = 0; i < question.size(); i++){
            StringBuilder sb = new StringBuilder();
            sb.append("问题：").append(question.get(i)).append("\n")
                    .append("回答：").append(transcription.get(i)).append("\n");
            if(answer.size()>i){
                sb.append("标准答案：").append(answer.get(i));
            }

            messages.add(ChatMessage.builder()
                    .role(ChatMessageRole.SYSTEM)
                    .content(sb.toString())
                    .build());
        }
        return doChat(messages);
    }
    /**
     * 更通用的方法，允许用户传入任意条消息列表
     *
     * @param chatMessageList
     * @return
     */
    public String doChat(List<ChatMessage> chatMessageList) {
        // 单次调用
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                // 指定您创建的方舟推理接入点 ID，此处已帮您修改为您的推理接入点 ID
                .model(modelName)
                .messages(chatMessageList)
                .build();

        List<ChatCompletionChoice> choiceList = arkService.createChatCompletion(chatCompletionRequest)
                .getChoices();
        if (CollUtil.isEmpty(choiceList)) {
            throw new RuntimeException("AI 没有返回任何内容");
        }
        String content = (String) choiceList.get(0).getMessage().getContent();
        System.out.println("AI 返回内容：" + content);
        return content;
    }
}
