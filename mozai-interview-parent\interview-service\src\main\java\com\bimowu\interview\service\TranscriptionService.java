package com.bimowu.interview.service;

import com.bimowu.interview.model.InterviewSpeechRecord;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 转写服务接口
 */
public interface TranscriptionService {
    /**
     * 将音频转换为文字
     * @param audioFile 音频文件
     * @return 识别的文字内容
     */
    String speechToText(MultipartFile audioFile);

    /**
     * 保存面试问题的转写结果
     * @param audioFile 音频文件
     * @param questionIndex 问题索引
     * @param interviewId 面试ID
     * @return 转写的文本
     */
    String saveTranscription(MultipartFile audioFile, Integer questionIndex, String interviewId);

    /**
     * 获取面试的所有转写结果
     * @param interviewId 面试ID
     * @return 问题索引到转写文本的映射
     */
    Map<Integer, String> getInterviewTranscriptions(String interviewId);

    /**
     * 对话转写
     * 
     * @param videoUrl 视频URL
     * @return 任务ID
     */
    String dialogTranslate(String videoUrl);
    
    /**
     * 章节转写
     * 
     * @param videoUrl 视频URL
     * @return 任务ID
     */
    String chapterTranslate(String videoUrl);

    String questionsAnsweringTranslate(String videoUrl);

    /**
     * 获取转写结果
     * 
     * @param taskId 任务ID
     * @param type 转写类型（dialog/chapter/qa）
     * @param interviewId 面试ID
     * @return 转写结果列表
     */
    List<Object> transcriptionResult(String taskId, String type, String interviewId);
}