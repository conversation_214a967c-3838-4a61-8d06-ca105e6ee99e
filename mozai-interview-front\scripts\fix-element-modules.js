const fs = require('fs');
const path = require('path');

console.log('🔧 修复 Element Plus 模块引用问题');

function fixElementPlusModules() {
  const elementPlusPath = path.join(__dirname, '../node_modules/element-plus');
  
  if (!fs.existsSync(elementPlusPath)) {
    console.log('❌ Element Plus 未安装');
    return false;
  }
  
  console.log('📁 Element Plus 路径:', elementPlusPath);
  
  // 1. 修复 es/index.mjs 文件
  const esIndexPath = path.join(elementPlusPath, 'es/index.mjs');
  if (fs.existsSync(esIndexPath)) {
    console.log('🔨 修复 es/index.mjs...');
    let content = fs.readFileSync(esIndexPath, 'utf8');
    
    // 替换所有有问题的引用
    const replacements = [
      {
        from: /\.\/hooks\/use-prevent-globalThis\/index\.mjs/g,
        to: './hooks/use-global-config/index.mjs'
      },
      {
        from: /\.\/components\/config-provider\/src\/hooks\/use-globalThis-config\.mjs/g,
        to: './components/config-provider/src/hooks/use-global-config.mjs'
      },
      {
        from: /\.\/hooks\/use-globalThis\/index\.mjs/g,
        to: './hooks/use-global-config/index.mjs'
      }
    ];
    
    let modified = false;
    replacements.forEach(({ from, to }) => {
      if (from.test(content)) {
        content = content.replace(from, to);
        modified = true;
        console.log(`✅ 替换: ${from.source} -> ${to}`);
      }
    });
    
    if (modified) {
      fs.writeFileSync(esIndexPath, content);
      console.log('✅ es/index.mjs 修复完成');
    } else {
      console.log('ℹ️ es/index.mjs 无需修复');
    }
  }
  
  // 2. 创建缺失的文件
  const missingFiles = [
    'es/hooks/use-prevent-globalThis/index.mjs',
    'es/hooks/use-globalThis/index.mjs',
    'es/components/config-provider/src/hooks/use-globalThis-config.mjs'
  ];
  
  missingFiles.forEach(filePath => {
    const fullPath = path.join(elementPlusPath, filePath);
    const dir = path.dirname(fullPath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`🔨 创建缺失文件: ${filePath}`);
      
      // 确保目录存在
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      // 创建一个简单的导出文件
      const content = `// Auto-generated fallback file
export default {};
export const useGlobalConfig = () => ({});
`;
      fs.writeFileSync(fullPath, content);
      console.log(`✅ 已创建: ${filePath}`);
    }
  });
  
  // 3. 检查并修复 package.json 中的导出
  const packageJsonPath = path.join(elementPlusPath, 'package.json');
  if (fs.existsSync(packageJsonPath)) {
    console.log('🔨 检查 package.json 导出...');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // 确保有正确的导出配置
    if (!packageJson.exports) {
      packageJson.exports = {};
    }
    
    packageJson.exports['.'] = {
      import: './es/index.mjs',
      require: './lib/index.js'
    };
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ package.json 导出配置已更新');
  }
  
  return true;
}

function main() {
  try {
    const success = fixElementPlusModules();
    if (success) {
      console.log('🎉 Element Plus 模块修复完成！');
      console.log('💡 现在可以尝试构建: npm run build');
    } else {
      console.log('❌ 修复失败');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ 修复过程中出错:', error.message);
    process.exit(1);
  }
}

main();
