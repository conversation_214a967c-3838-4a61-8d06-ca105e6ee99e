# Element Plus 模块解析错误修复方案

## 错误详情
```
Could not resolve "./hooks/use-prevent-globalThis/index.mjs" from "node_modules/element-plus/es/index.mjs"
RollupError: Could not resolve "./hooks/use-prevent-globalThis/index.mjs"
```

## 解决方案

### 🚀 快速修复（推荐）

1. **运行自动修复脚本**
```bash
cd mozai-interview-front

# Linux/macOS
chmod +x fix-node16-build.sh && ./fix-node16-build.sh

# Windows
fix-node16-build.bat
```

2. **或者使用命令行**
```bash
# 清理并重装依赖
npm run fix:deps

# 使用兼容构建
npm run build:simple
```

### 🔧 手动修复步骤

1. **清理环境**
```bash
rm -rf node_modules package-lock.json dist-interview-ai .vite
```

2. **重新安装依赖**
```bash
npm install
```

3. **尝试不同构建方式**
```bash
# 方式1: 专用配置
npm run build:node16

# 方式2: 简化构建（推荐）
npm run build:simple

# 方式3: 强制构建
SKIP_OPTIONAL_DEPENDENCIES=true npm run build
```

## 关键修改

### 依赖版本调整
- **Element Plus**: 2.3.12 → 2.2.36 (兼容 Vite 4.x)
- **Vite**: 6.3.5 → 4.4.12 (支持 Node.js 16)
- **TypeScript**: 5.2.2 → 4.9.5 (稳定版本)
- **Vue Plugin**: 5.2.4 → 4.4.0 (匹配 Vite 版本)

### 新增配置文件
- `vite.config.node16.ts` - Node.js 16 专用配置
- `scripts/build-simple.js` - 多重备用构建方案
- `scripts/build-node16.js` - 专用构建脚本

## 验证构建

运行测试脚本验证修复：
```bash
chmod +x test-build.sh && ./test-build.sh
```

成功标志：
- ✅ 无模块解析错误
- ✅ 生成 `dist-interview-ai` 目录
- ✅ 包含完整的静态资源

## 故障排除

### 如果仍然失败

1. **检查 Node.js 版本**
```bash
node --version  # 应该是 16.x
```

2. **使用 yarn 替代 npm**
```bash
yarn install
yarn build
```

3. **手动安装特定版本**
```bash
npm install element-plus@2.2.36 vite@4.4.12 --save-exact
```

4. **最后的备用方案**
```bash
# 跳过所有可选依赖
SKIP_OPTIONAL_DEPENDENCIES=true npm install --no-optional
npx vite build --force
```

## 长期解决方案

**强烈建议升级到 Node.js 18+**，这样可以：
- 使用最新版本的构建工具
- 获得更好的性能和安全性
- 避免兼容性问题

升级后可以恢复使用：
- Vite 6.x
- Element Plus 2.3.x
- TypeScript 5.x
