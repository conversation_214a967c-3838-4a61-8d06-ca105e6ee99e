const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 最终构建解决方案');
console.log('Node.js 版本:', process.version);

async function cleanEnvironment() {
  console.log('🧹 清理构建环境...');
  const dirsToClean = ['dist-interview-ai', '.vite', 'node_modules/.vite'];
  
  dirsToClean.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      console.log(`✅ 已清理: ${dir}`);
    }
  });
}

async function installDependencies() {
  console.log('📦 安装依赖...');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ 依赖安装完成');
  } catch (error) {
    console.log('⚠️ npm install 失败，尝试清理缓存...');
    execSync('npm cache clean --force', { stdio: 'inherit' });
    execSync('npm install', { stdio: 'inherit' });
  }
}

async function fixElementPlus() {
  console.log('🔧 修复 Element Plus...');
  try {
    // 1. 安装特定版本
    execSync('npm install element-plus@2.0.6 --save-exact', { stdio: 'inherit' });
    
    // 2. 运行模块修复脚本
    execSync('node scripts/fix-element-modules.js', { stdio: 'inherit' });
    
    console.log('✅ Element Plus 修复完成');
  } catch (error) {
    console.log('⚠️ Element Plus 修复失败:', error.message);
  }
}

async function tryBuild(config, description) {
  console.log(`🏗️ 尝试构建: ${description}`);
  try {
    const command = config ? `npx vite build --config ${config}` : 'npx vite build';
    execSync(command, { 
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_OPTIONS: '--max-old-space-size=4096',
        SKIP_OPTIONAL_DEPENDENCIES: 'true'
      }
    });
    
    // 检查构建结果
    if (fs.existsSync('dist-interview-ai') && fs.readdirSync('dist-interview-ai').length > 0) {
      console.log('✅ 构建成功！');
      return true;
    }
  } catch (error) {
    console.log(`❌ 构建失败: ${error.message}`);
  }
  return false;
}

async function main() {
  try {
    // 1. 清理环境
    await cleanEnvironment();
    
    // 2. 安装依赖
    await installDependencies();
    
    // 3. 修复 Element Plus
    await fixElementPlus();
    
    // 4. 尝试不同的构建配置
    const buildConfigs = [
      { config: 'vite.config.bypass.ts', desc: 'Bypass ES Modules' },
      { config: 'vite.config.legacy.ts', desc: 'Legacy CommonJS' },
      { config: 'vite.config.node16.ts', desc: 'Node.js 16 Optimized' },
      { config: null, desc: 'Default Config' }
    ];
    
    for (const { config, desc } of buildConfigs) {
      if (config && !fs.existsSync(config)) {
        console.log(`⚠️ 配置文件不存在: ${config}`);
        continue;
      }
      
      console.log('\n' + '='.repeat(50));
      const success = await tryBuild(config, desc);
      
      if (success) {
        console.log(`🎉 使用 "${desc}" 构建成功！`);
        
        // 显示构建结果
        console.log('\n📁 构建产物:');
        try {
          const files = fs.readdirSync('dist-interview-ai');
          files.forEach(file => {
            const stat = fs.statSync(path.join('dist-interview-ai', file));
            const size = stat.isDirectory() ? '[DIR]' : `${(stat.size / 1024).toFixed(1)}KB`;
            console.log(`  ${file} ${size}`);
          });
        } catch (e) {
          console.log('  构建目录已生成');
        }
        
        return;
      }
      
      // 清理失败的构建
      if (fs.existsSync('dist-interview-ai')) {
        fs.rmSync('dist-interview-ai', { recursive: true, force: true });
      }
    }
    
    // 所有方式都失败了
    console.log('\n❌ 所有构建方式都失败了');
    console.log('\n💡 最后的建议:');
    console.log('1. 升级到 Node.js 18+: nvm install 18 && nvm use 18');
    console.log('2. 使用 yarn: rm -rf node_modules && yarn install && yarn build');
    console.log('3. 手动下载预构建版本');
    console.log('4. 考虑使用其他 UI 库 (如 Ant Design Vue)');
    
    process.exit(1);
    
  } catch (error) {
    console.error('❌ 构建过程出错:', error.message);
    process.exit(1);
  }
}

main();
