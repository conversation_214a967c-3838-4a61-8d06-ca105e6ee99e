const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔧 简化构建脚本 (Node.js 16 兼容)');

try {
  // 1. 清理
  console.log('🧹 清理构建目录...');
  if (fs.existsSync('dist-interview-ai')) {
    fs.rmSync('dist-interview-ai', { recursive: true, force: true });
  }
  
  // 2. 设置环境变量
  const env = {
    ...process.env,
    NODE_OPTIONS: '--max-old-space-size=4096',
    SKIP_OPTIONAL_DEPENDENCIES: 'true',
    VITE_LEGACY_BUILD: 'true'
  };
  
  // 3. 尝试不同的构建方式
  console.log('🏗️ 尝试构建方式 1: 使用专用配置...');
  try {
    execSync('npx vite build --config vite.config.node16.ts', { 
      stdio: 'inherit',
      env
    });
    console.log('✅ 构建成功！');
    return;
  } catch (error) {
    console.log('⚠️ 方式 1 失败，尝试方式 2...');
  }
  
  // 4. 备用方式：使用默认配置但跳过问题依赖
  console.log('🏗️ 尝试构建方式 2: 跳过问题依赖...');
  try {
    execSync('npx vite build --mode production', { 
      stdio: 'inherit',
      env: {
        ...env,
        VITE_SKIP_ELEMENT_PLUS_HOOKS: 'true'
      }
    });
    console.log('✅ 构建成功！');
    return;
  } catch (error) {
    console.log('⚠️ 方式 2 失败，尝试方式 3...');
  }
  
  // 5. 最后的备用方式：强制使用 CommonJS
  console.log('🏗️ 尝试构建方式 3: 强制 CommonJS...');
  execSync('npx vite build --force', { 
    stdio: 'inherit',
    env: {
      ...env,
      NODE_ENV: 'production'
    }
  });
  
  console.log('✅ 构建成功！');
  
} catch (error) {
  console.error('❌ 所有构建方式都失败了:', error.message);
  
  console.log('\n💡 请尝试以下解决方案:');
  console.log('1. 升级到 Node.js 18+');
  console.log('2. 使用 yarn 替代 npm: yarn install && yarn build');
  console.log('3. 手动安装兼容版本: npm install element-plus@2.2.36');
  console.log('4. 联系开发团队获取帮助');
  
  process.exit(1);
}
