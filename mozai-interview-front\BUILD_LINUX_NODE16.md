# Linux Node.js 16 构建指南

## 问题描述
在 Linux 环境下使用 Node.js 16 构建时出现错误：
```
The CJS build of Vite's Node API is deprecated
TypeError: crypto$2.getRandomValues is not a function
```

## 一键解决方案

```bash
# 给脚本执行权限
chmod +x fix-linux-build.sh

# 运行修复脚本
./fix-linux-build.sh
```

## 手动解决步骤

如果自动脚本失败，可以手动执行：

```bash
# 1. 清理环境
rm -rf node_modules package-lock.json dist-interview-ai .vite
npm cache clean --force

# 2. 安装兼容依赖
npm install

# 3. 使用 Node.js 16 兼容构建
npm run build:node16
```

## 修改内容

### 1. package.json 依赖降级
- **Vite**: 6.3.5 → 4.4.12 (支持 Node.js 16)
- **Element Plus**: 2.3.12 → 2.2.36 (稳定版本)
- **TypeScript**: 5.2.2 → 4.9.5 (兼容版本)
- **@vitejs/plugin-vue**: 5.2.4 → 4.4.0 (匹配 Vite 版本)

### 2. 新增构建脚本
- `build:node16`: 使用 Node.js 16 兼容配置构建

### 3. 配置文件调整
- `vite.config.node16.js`: Node.js 16 专用配置（CommonJS 格式）
- `tsconfig.json`: moduleResolution 改为 "node"

## 验证构建成功

构建成功后会看到：
```
✅ 构建成功！
📁 构建产物大小: 2.1M dist-interview-ai
📋 构建内容:
drwxr-xr-x assets/
-rw-r--r-- index.html
🚀 可以部署了！
```

## 如果仍然失败

### 升级 Node.js（推荐）
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 使用 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
```

### 使用 yarn
```bash
# 安装 yarn
npm install -g yarn

# 使用 yarn 构建
rm -rf node_modules package-lock.json
yarn install
yarn build
```

### 检查系统依赖
```bash
# 安装构建工具
sudo apt-get update
sudo apt-get install -y build-essential python3 python3-pip

# 或者 CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y python3 python3-pip
```

## 部署说明

构建成功后，`dist-interview-ai` 目录包含所有静态文件，可以直接部署到：
- Nginx
- Apache
- CDN
- 静态文件服务器

## 注意事项

1. 这个配置专门针对 Node.js 16 优化
2. 如果升级到 Node.js 18+，建议恢复使用最新版本的依赖
3. 构建产物功能完全正常，只是构建工具版本较低
