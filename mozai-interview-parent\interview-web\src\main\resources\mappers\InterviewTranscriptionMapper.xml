<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bimowu.interview.dao.InterviewTranscriptionMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.bimowu.interview.model.InterviewTranscription">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="interview_id" property="interviewId" jdbcType="VARCHAR"/>
        <result column="question_index" property="questionIndex" jdbcType="INTEGER"/>
        <result column="question" property="question" jdbcType="VARCHAR"/>
        <result column="transcription" property="transcription" jdbcType="VARCHAR"/>
        <result column="audio_url" property="audioUrl" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, interview_id, question_index, question, transcription, audio_url, create_time, update_time
    </sql>
    
    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.bimowu.interview.model.InterviewTranscription"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO interview_transcription (
            interview_id, question_index, question, transcription, audio_url, create_time, update_time
        ) VALUES (
            #{interviewId,jdbcType=VARCHAR},
            #{questionIndex,jdbcType=INTEGER},
            #{question,jdbcType=VARCHAR},
            #{transcription,jdbcType=VARCHAR},
            #{audioUrl,jdbcType=VARCHAR},
            NOW(),
            NOW()
        )
    </insert>
    
    <!-- 批量插入记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO interview_transcription (
            interview_id, question_index, question, transcription, audio_url, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
            #{item.interviewId,jdbcType=VARCHAR},
            #{item.questionIndex,jdbcType=INTEGER},
            #{item.question,jdbcType=VARCHAR},
            #{item.transcription,jdbcType=VARCHAR},
            #{item.audioUrl,jdbcType=VARCHAR},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>
    
    <!-- 更新记录 -->
    <update id="update" parameterType="com.bimowu.interview.model.InterviewTranscription">
        UPDATE interview_transcription
        <set>
            <if test="question != null">
                question = #{question,jdbcType=VARCHAR},
            </if>
            <if test="transcription != null">
                transcription = #{transcription,jdbcType=VARCHAR},
            </if>
            <if test="audioUrl != null">
                audio_url = #{audioUrl,jdbcType=VARCHAR},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>
    
    <!-- 根据ID查询记录 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_transcription
        WHERE id = #{id,jdbcType=BIGINT}
    </select>
    
    <!-- 根据面试ID和问题索引查询记录 -->
    <select id="selectByInterviewIdAndQuestionIndex" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_transcription
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
        AND question_index = #{questionIndex,jdbcType=INTEGER}
    </select>
    
    <!-- 根据面试ID查询所有记录 -->
    <select id="selectByInterviewId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_transcription
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
        ORDER BY question_index ASC
    </select>
    
    <!-- 根据条件查询记录 -->
    <select id="selectByCondition" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_transcription
        <where>
            <if test="interviewId != null">
                AND interview_id = #{interviewId,jdbcType=VARCHAR}
            </if>
            <if test="questionIndex != null">
                AND question_index = #{questionIndex,jdbcType=INTEGER}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY interview_id, question_index ASC
    </select>
    
    <!-- 根据ID删除记录 -->
    <delete id="deleteById">
        DELETE FROM interview_transcription
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>
    
    <!-- 根据面试ID删除记录 -->
    <delete id="deleteByInterviewId">
        DELETE FROM interview_transcription
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
    </delete>
</mapper> 