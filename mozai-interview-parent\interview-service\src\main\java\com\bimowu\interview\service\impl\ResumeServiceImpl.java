package com.bimowu.interview.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bimowu.interview.service.ResumeService;
import com.bimowu.interview.utils.AIModelClient;
import com.bimowu.interview.utils.DocumentParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class ResumeServiceImpl implements ResumeService {

    @Autowired
    private DocumentParser documentParser;
    
    @Autowired
    private AIModelClient aiModelClient;
    
    @Autowired
    private RestTemplate restTemplate;
    
    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir");

    @Override
    public String extractResumeText(MultipartFile file) throws Exception {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new IllegalArgumentException("上传的文件不能为空");
        }
        
        log.info("开始解析简历文件: {}", file.getOriginalFilename());
        
        // 获取文件扩展名
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (extension == null) {
            throw new IllegalArgumentException("无法确定文件类型");
        }
        
        // 检查文件类型
        extension = extension.toLowerCase();
        if (!extension.equals("pdf") && !extension.equals("doc") && !extension.equals("docx")) {
            throw new IllegalArgumentException("仅支持PDF、DOC、DOCX格式文件");
        }
        
        // 将文件保存到临时目录
        String tempFileName = UUID.randomUUID().toString() + "." + extension;
        Path tempFilePath = Paths.get(TEMP_DIR, tempFileName);
        Files.write(tempFilePath, file.getBytes());
        
        try {
            // 调用文档解析器提取文本
            log.info("调用文档解析器解析文件: {}", tempFilePath);
            String resumeText = documentParser.parseDocument(tempFilePath.toFile(), extension);
            log.info("简历文本解析完成，文本长度: {}", resumeText.length());
            return resumeText;
        } finally {
            // 删除临时文件
            Files.deleteIfExists(tempFilePath);
            log.info("临时文件已删除: {}", tempFilePath);
        }
    }

    @Override
    public List<String> generateInterviewQuestions(String resumeText) throws Exception {
        // 使用默认参数调用带完整参数的方法
        return generateInterviewQuestions(resumeText, "hr", "dev", "3-5");
    }
    
    @Override
    public List<String> generateInterviewQuestions(String resumeText, String interviewStage, 
                                                String interviewPosition, String interviewExperience) throws Exception {
        if (resumeText == null || resumeText.trim().isEmpty()) {
            throw new IllegalArgumentException("简历内容不能为空");
        }
        //通过http请求获取面试问题，请求地址：如下，返回String Json
        //HR面试请求地址：http://127.0.0.1:7091/chatAPI/interviewChat
        
        log.info("开始生成面试问题，简历文本长度: {}", resumeText.length());
        log.info("面试类型参数 - 面试环节: {}, 面试岗位: {}, 工作经验: {}", 
                interviewStage, interviewPosition, interviewExperience);
        
        try {
            // 根据面试环节和工作经验选择不同的API接口URL
            String baseUrl = "http://127.0.0.1:7091/chatAPI/";
            String url="";
            
            if ("hr".equalsIgnoreCase(interviewStage)) {
                // HR面试使用统一接口
                url = baseUrl + "interviewChatHR";
            } else if ("tech".equalsIgnoreCase(interviewStage)) {
                if("dev".equalsIgnoreCase(interviewPosition)){
                    // 技术面试根据工作经验选择不同接口
                    if ("fresh".equalsIgnoreCase(interviewExperience)) {
                        url = baseUrl + "interviewChatTEC0"; // 应届生
                    } else if ("1-3".equalsIgnoreCase(interviewExperience)) {
                        url = baseUrl + "interviewChatTEC1"; // 1-3年
                    } else if ("3-5".equalsIgnoreCase(interviewExperience)) {
                        url = baseUrl + "interviewChatTEC3"; // 3-5年
                    } else if ("5+".equalsIgnoreCase(interviewExperience)) {
                        url = baseUrl + "interviewChatTEC5"; // 5年以上
                    } else {
                        // 默认使用应届生经验的接口
                        log.warn("未知的工作经验类型: {}，使用默认接口", interviewExperience);
                        url = baseUrl + "interviewChatTEC0";
                    }
                } else if ("support".equalsIgnoreCase(interviewPosition)) {
                    url = baseUrl + "interviewChatSup"; // 3-5年
                }
            } else {
                // 默认使用HR面试接口
                log.warn("未知的面试环节类型: {}，使用默认接口", interviewStage);
                url = baseUrl + "interviewChatHR";
            }
            
            log.info("发送HTTP请求到: {}，超时设置为120秒", url);
            
            // 构建请求体，包含面试类型参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("question", resumeText);

            
            try {
                // 发送POST请求
                ResponseEntity<String> response = restTemplate.postForEntity(url, requestBody, String.class);
                
                // 检查响应状态
                if (response.getStatusCode() != HttpStatus.OK) {
                    log.error("HTTP请求失败，状态码: {}", response.getStatusCode());
                    throw new Exception("获取面试问题失败，HTTP状态码: " + response.getStatusCode());
                }
                
                log.info("HTTP请求成功，状态码: {}", response.getStatusCode());
                
                // 解析响应的JSON字符串
                String jsonResponse = response.getBody();
                if (jsonResponse == null || jsonResponse.isEmpty()) {
                    log.error("HTTP响应内容为空");
                    throw new Exception("获取面试问题失败，响应为空");
                }
                
                // 解析JSON并转换为问题列表
                try {
                    log.info("收到的响应内容: {}", jsonResponse);
                    
                    // 清理可能的前缀，如 ```json
                    if (jsonResponse.contains("```json")) {
                        jsonResponse = jsonResponse.substring(jsonResponse.indexOf("["), jsonResponse.lastIndexOf("]") + 1);
                        log.info("清理前缀后的JSON: {}", jsonResponse);
                    }
                    
                    List<String> questions = new ArrayList<>();

                    try {
                        JSONArray questionsArray = JSON.parseArray(jsonResponse);
                        if (questionsArray != null && !questionsArray.isEmpty()) {
                            for (int i = 0; i < questionsArray.size(); i++) {
                                String question = questionsArray.getString(i);
                                if (question != null && !question.trim().isEmpty()) {
                                    questions.add(question);
                                }
                            }
                        }
                    } catch (Exception e2) {
                        log.error("无法将响应解析为JSONArray", e2);
                        throw new Exception("解析面试问题失败: " + e2.getMessage(), e2);
                    }
                    
                    if (questions.isEmpty()) {
                        log.warn("生成的问题列表为空");
                    } else {
                        log.info("成功生成{}个面试问题", questions.size());
                    }
                    
                    return questions;
                } catch (Exception e) {
                    log.error("解析JSON响应失败", e);
                    throw new Exception("解析面试问题失败: " + e.getMessage(), e);
                }
            } catch (org.springframework.web.client.ResourceAccessException e) {
                log.error("HTTP请求访问资源异常（可能是超时）: {}", e.getMessage(), e);
                if (e.getCause() instanceof java.net.SocketTimeoutException) {
                    log.error("请求超时，已设置超时时间为120秒，但仍然超时");
                }
                throw e;
            }
        } catch (Exception e) {
            // 如果HTTP请求失败，使用AIModelClient作为备用
            log.warn("HTTP请求失败，使用AIModelClient作为备用: {}", e.getMessage());
            log.debug("详细异常信息", e);
            
            // 注意：这里调用原始的AIModelClient方法，没有传递面试类型参数
            // 未来可以考虑修改AIModelClient，使其也支持面试类型参数
            List<String> questions = aiModelClient.generateInterviewQuestions(resumeText);
            log.info("使用备用方法成功生成{}个面试问题", questions.size());
            return questions;
        }
    }
} 