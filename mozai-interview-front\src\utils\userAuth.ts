// 默认用户列表
export const defaultUsers = [
  { username: 'admin', password: 'admin123', role: 'admin' },
  { username: 'user', password: 'user123', role: 'user' },
  { username: 'test', password: 'test123', role: 'user' }
]

// 初始化用户数据
export const initUserAuth = () => {
  // 将默认用户添加到localStorage，以便在页面刷新后仍然可用
  localStorage.setItem('defaultUsers', JSON.stringify(defaultUsers))
  
  console.log('用户认证系统初始化完成，默认用户已添加')
}

// 验证用户登录
export const authenticateUser = (username: string, password: string): any => {
  // 1. 检查默认用户
  let user = defaultUsers.find(
    user => user.username === username && user.password === password
  )
  
  // 2. 如果默认用户中没有，检查已注册用户
  if (!user) {
    const registeredUsersStr = localStorage.getItem('registeredUsers')
    const registeredUsers = registeredUsersStr ? JSON.parse(registeredUsersStr) : []
    
    user = registeredUsers.find(
      (registeredUser: any) => registeredUser.username === username && 
              registeredUser.password === password
    )
  }
  
  return user
}

// 登录用户并保存数据
export const loginUser = (user: any): boolean => {
  if (!user) return false
  // 将用户信息存储在localStorage中
  const userData = {
    username: user.username,
    role: user.role,
    token: 'mock-token-' + Date.now(),
    avatar: '',
    // userId写死
    userId: 1,
  }
  
  localStorage.setItem('user', JSON.stringify(userData))
  return true
}

// 检查用户是否已登录
export const isAuthenticated = (): boolean => {
  return !!localStorage.getItem('user')
}

// 获取当前登录用户信息
export const getCurrentUser = (): any => {
  const userStr = localStorage.getItem('user')
  return userStr ? JSON.parse(userStr) : null
}

// 注册新用户
export const registerUser = (
  username: string, 
  email: string, 
  password: string
): boolean => {
  // 检查用户名是否已存在
  const existingUsersStr = localStorage.getItem('registeredUsers')
  const existingUsers = existingUsersStr ? JSON.parse(existingUsersStr) : []
  
  if (existingUsers.some((user: any) => user.username === username)) {
    return false
  }
  
  // 创建新用户
  const newUser = {
    username,
    email,
    password,
    role: 'user',
    createdAt: new Date().toISOString()
  }
  
  // 将新用户添加到注册用户列表
  existingUsers.push(newUser)
  localStorage.setItem('registeredUsers', JSON.stringify(existingUsers))
  
  return true
}

// 登出当前用户
export const logoutUser = (): void => {
  localStorage.removeItem('user')
} 