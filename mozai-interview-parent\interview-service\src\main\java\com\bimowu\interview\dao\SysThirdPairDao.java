package com.bimowu.interview.dao;

import com.bimowu.interview.model.SysThirdPair;
import com.bimowu.interview.utils.bean.CommonQueryBean;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 
 * SysThirdPair数据库操作接口类
 * 
 **/

@Repository
public interface SysThirdPairDao{


	/**
	 * 
	 * 查询（根据主键ID查询）
	 * 
	 **/
	SysThirdPair selectByPrimaryKey(@Param("id") Long id);

	/**
	 * 
	 * 删除（根据主键ID删除）
	 * 
	 **/
	int deleteByPrimaryKey(@Param("id") Long id);

	/**
	 * 
	 * 添加
	 * 
	 **/
	int insert(SysThirdPair record);

	/**
	 * 
	 * 修改 （匹配有值的字段）
	 * 
	 **/
	int updateByPrimaryKeySelective(SysThirdPair record);

	/**
	 * 
	 * list分页查询
	 * 
	 **/
	List<SysThirdPair> list4Page(@Param("record") SysThirdPair record, @Param("commonQueryParam") CommonQueryBean query);

	/**
	 * 
	 * count查询
	 * 
	 **/
	long count(@Param("record") SysThirdPair record);

	/**
	 * 
	 * list查询
	 * 
	 **/
	List<SysThirdPair> list(@Param("record") SysThirdPair record);

}