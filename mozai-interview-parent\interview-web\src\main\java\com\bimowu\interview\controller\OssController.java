package com.bimowu.interview.controller;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.bimowu.interview.base.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * OSS控制器
 * 提供OSS直传所需的临时授权
 */
@RestController
@RequestMapping("/oss")
@Api(tags = "OSS操作接口")
@Slf4j
public class OssController {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    @Value("${aliyun.oss.region}")
    private String region;

    /**
     * STS临时授权角色ARN，需要在阿里云RAM控制台创建
     * 格式：acs:ram::账号ID:role/角色名
     * 这里使用一个示例值，实际使用时需要替换为真实值
     */
    @Value("${aliyun.oss.roleArn}")
    private String roleArn;

    /**
     * 获取OSS临时授权
     * 用于前端直传文件到OSS
     *
     * @return STS临时授权信息
     */
    @GetMapping("/sts")
    @ApiOperation("获取OSS临时授权")
    public BaseResponse<Map<String, Object>> getStsToken() {
        try {
            // 创建DefaultAcsClient实例
            DefaultProfile profile = DefaultProfile.getProfile(region, accessKeyId, accessKeySecret);
            DefaultAcsClient client = new DefaultAcsClient(profile);

            // 创建AssumeRoleRequest并设置参数
            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setSysProtocol(ProtocolType.HTTPS);
            request.setSysMethod(MethodType.POST);
            
            // 设置角色ARN
            request.setRoleArn(roleArn);
            // 设置会话名称，建议使用不同的名称
            request.setRoleSessionName("interview-oss-session-" + System.currentTimeMillis());
            // 设置过期时间，单位为秒，最小900，最大3600
            request.setDurationSeconds(900L);
            
            // 发送请求，获取响应
            AssumeRoleResponse response = client.getAcsResponse(request);
            
            // 构建返回结果
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("accessKeyId", accessKeyId);
            result.put("accessKeySecret", accessKeySecret);
            result.put("stsToken", response.getCredentials().getSecurityToken());
            result.put("region", region);
            result.put("bucket", bucketName);
            result.put("endpoint", endpoint);
            
            log.info("获取OSS临时授权成功");
            return new BaseResponse<>(0, "获取OSS临时授权成功", result);
        } catch (ClientException e) {
            log.error("获取OSS临时授权失败: {}", e.getMessage(), e);
            return new BaseResponse<>(500, "获取OSS临时授权失败: " + e.getMessage(), null);
        }
    }
} 