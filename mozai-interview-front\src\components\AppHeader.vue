<template>
  <header class="app-header">
    <div class="container">
      <div class="logo" @click="navigateTo('/')">
        <el-icon class="logo-icon"><Monitor /></el-icon>
        <span class="logo-text">墨崽AI面试</span>
      </div>

      <nav class="nav-menu" v-if="isLoggedIn">
        <el-menu mode="horizontal" :ellipsis="false" router :default-active="activeIndex">
          <el-menu-item index="/">首页</el-menu-item>
          <el-menu-item index="/selection">面试</el-menu-item>
          <el-menu-item index="/interviews">我的面试</el-menu-item>
        </el-menu>
      </nav>

      <div class="user-actions">
        <template v-if="isLoggedIn">
          <el-dropdown trigger="click">
            <div class="user-avatar">
              <el-avatar :size="32" :src="userAvatar">{{ userInitials }}</el-avatar>
              <span class="username">{{ username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item divided @click="logout">
                  <el-icon><Switch /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template v-else>
          <el-button type="primary" @click="navigateTo('/login')">登录</el-button>
        </template>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Monitor, ArrowDown, User, Setting, Switch } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/store'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const activeIndex = computed(() => route.path)

// 使用pinia store中的登录状态
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 使用pinia store中的用户信息
const username = computed(() => userStore.nickname)

const userAvatar = computed(() => {
  return userStore.userInfo.avatar || ''
})

const userInitials = computed(() => {
  return username.value.slice(0, 1).toUpperCase()
})

const navigateTo = (path: string) => {
  router.push(path)
}

const logout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    console.error('退出登录失败:', error)
    ElMessage.error('退出登录失败，请重试')
  }
}

onMounted(async () => {
  // 页面加载时尝试获取用户信息
  if (userStore.token && !userStore.userInfo.userId) {
    try {
      await userStore.fetchUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
})
</script>

<style scoped>
.app-header {
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.container {
  max-width: 1200px;
  height: 60px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.logo-icon {
  font-size: 24px;
  color: #409EFF;
  margin-right: 8px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.nav-menu {
  flex: 1;
  margin-left: 40px;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: #606266;
}
</style> 