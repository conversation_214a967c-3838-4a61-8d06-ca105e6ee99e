<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bimowu.interview.dao.SysThirdPairDao">

	<resultMap id="SysThirdPair" type="com.bimowu.interview.model.SysThirdPair" >
		<result column="id" property="id"/>
		<result column="sys_type" property="sysType"/>
		<result column="sys_first" property="sysFirst"/>
		<result column="sys_second" property="sysSecond"/>
		<result column="sys_third" property="sysThird"/>
		<result column="state" property="state"/>
		<result column="memo" property="memo"/>
	</resultMap>

	<!-- 通用查询结果列-->
	<sql id="Base_Column_List">
		id,
		sys_type,
		sys_first,
		sys_second,
		sys_third,
		state,
		memo
	</sql>

	<!-- 查询（根据主键ID查询） -->
	<select id="selectByPrimaryKey" resultMap="SysThirdPair" parameterType="java.lang.Long">
		 SELECT
		 <include refid="Base_Column_List" />
		 FROM sys_third_pair
		 WHERE id = #{id}
	</select>

	<!--删除：根据主键ID删除-->
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		 DELETE FROM sys_third_pair
		 WHERE id = #{id}
	</delete>

	<!-- 添加 -->
	<insert id="insert" parameterType="com.bimowu.interview.model.SysThirdPair">
		 INSERT INTO sys_third_pair
 		(
			 id,
			 sys_type,
			 sys_first,
			 sys_second,
			 sys_third,
			 state,
			 memo
		) 
		 VALUES 
 		(
			 #{id},
			 #{sysType},
			 #{sysFirst},
			 #{sysSecond},
			 #{sysThird},
			 #{state},
			 #{memo}
 		) 
		 <selectKey keyProperty="id" resultType="Long" order="AFTER">
			 select LAST_INSERT_ID()
		 </selectKey>
	</insert>

	<!-- 修 改-->
	<update id="updateByPrimaryKeySelective" parameterType="com.bimowu.interview.model.SysThirdPair">
		 UPDATE sys_third_pair
 		 <set> 
			<if test="sysType != null and sysType != ''">
				 sys_type = #{sysType},
			</if>
			<if test="sysFirst != null and sysFirst != ''">
				 sys_first = #{sysFirst},
			</if>
			<if test="sysSecond != null and sysSecond != ''">
				 sys_second = #{sysSecond},
			</if>
			<if test="sysThird != null and sysThird != ''">
				 sys_third = #{sysThird},
			</if>
			<if test="state != null">
				 state = #{state},
			</if>
			<if test="memo != null and memo != ''">
				 memo = #{memo},
			</if>

 		 </set>
		 WHERE id = #{id}
	</update>

	<!-- list4Page 分页查询-->
	<select id="list4Page" resultMap="SysThirdPair">
		 SELECT 
		 <include refid="Base_Column_List" />
		 from sys_third_pair
 		 where 1=1  
		<if test="record.id != null">
			 and id = #{record.id} 
		</if>
		<if test="record.sysType != null and record.sysType != ''">
			 and sys_type = #{record.sysType} 
		</if>
		<if test="record.sysFirst != null and record.sysFirst != ''">
			 and sys_first = #{record.sysFirst} 
		</if>
		<if test="record.sysSecond != null and record.sysSecond != ''">
			 and sys_second = #{record.sysSecond} 
		</if>
		<if test="record.sysThird != null and record.sysThird != ''">
			 and sys_third = #{record.sysThird} 
		</if>
		<if test="record.state != null">
			 and state = #{record.state} 
		</if>
		<if test="record.memo != null and record.memo != ''">
			 and memo = #{record.memo} 
		</if>
		<if test="commonQueryParam != null">
			<if test="commonQueryParam.start != null  and commonQueryParam.pageSize != null">
				 limit #{commonQueryParam.start}, #{commonQueryParam.pageSize}
			</if>
		</if>
	</select>
	<!-- count 总数-->
	<select id="count" resultType="long">
		 SELECT 
		 count(1) 
		 from sys_third_pair
 		 where 1=1  
		<if test="record.id != null">
			 and id = #{record.id} 
		</if>
		<if test="record.sysType != null and record.sysType != ''">
			 and sys_type = #{record.sysType} 
		</if>
		<if test="record.sysFirst != null and record.sysFirst != ''">
			 and sys_first = #{record.sysFirst} 
		</if>
		<if test="record.sysSecond != null and record.sysSecond != ''">
			 and sys_second = #{record.sysSecond} 
		</if>
		<if test="record.sysThird != null and record.sysThird != ''">
			 and sys_third = #{record.sysThird} 
		</if>
		<if test="record.state != null">
			 and state = #{record.state} 
		</if>
		<if test="record.memo != null and record.memo != ''">
			 and memo = #{record.memo} 
		</if>
	</select>
	<!-- list 查询-->
	<select id="list" resultMap="SysThirdPair">
		 SELECT 
		 <include refid="Base_Column_List" />
		 from sys_third_pair
 		 where 1=1  
		<if test="record.id != null">
			 and id = #{record.id} 
		</if>
		<if test="record.sysType != null and record.sysType != ''">
			 and sys_type = #{record.sysType} 
		</if>
		<if test="record.sysFirst != null and record.sysFirst != ''">
			 and sys_first = #{record.sysFirst} 
		</if>
		<if test="record.sysSecond != null and record.sysSecond != ''">
			 and sys_second = #{record.sysSecond} 
		</if>
		<if test="record.sysThird != null and record.sysThird != ''">
			 and sys_third = #{record.sysThird} 
		</if>
		<if test="record.state != null">
			 and state = #{record.state} 
		</if>
		<if test="record.memo != null and record.memo != ''">
			 and memo = #{record.memo} 
		</if>
	</select>
</mapper>