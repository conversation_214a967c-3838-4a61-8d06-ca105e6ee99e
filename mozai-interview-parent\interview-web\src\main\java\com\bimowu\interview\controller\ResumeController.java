package com.bimowu.interview.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bimowu.interview.base.BaseResponse;
import com.bimowu.interview.model.Interview;
import com.bimowu.interview.model.ResumeCategory;
import com.bimowu.interview.model.SysTodo;
import com.bimowu.interview.model.ResumeCategoryRelation;
import com.bimowu.interview.service.InterviewService;
import com.bimowu.interview.service.InterviewTranscriptionService;
import com.bimowu.interview.service.ResumeCategoryService;
import com.bimowu.interview.service.ResumeService;
import com.bimowu.interview.service.ResumeHrQuestionsService;
import com.bimowu.interview.service.ResumeProjectQuestionService;
import com.bimowu.interview.service.ResumeCategoryRelationService;
import com.bimowu.interview.service.impl.AiManager;
import com.bimowu.interview.dao.SysTodoMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/resume")
@Api(tags = "简历管理接口")
@Slf4j
public class ResumeController {

    @Autowired
    private ResumeService resumeService;

    @Autowired
    private ResumeCategoryService resumeCategoryService;
    
    @Autowired
    private ResumeCategoryRelationService resumeCategoryRelationService;

    @Autowired
    private InterviewService interviewService;

    @Autowired
    private InterviewTranscriptionService interviewTranscriptionService;

    @Autowired
    private ResumeHrQuestionsService resumeHrQuestionsService;

    @Autowired
    private ResumeProjectQuestionService resumeProjectQuestionService;
    
    @Autowired
    private SysTodoMapper sysTodoMapper;

    @Autowired
    private AiManager aiManager;

    @GetMapping("/categoryList")
    @ApiOperation("获取岗位列表")
    public BaseResponse<ResumeCategory> getResumeCategoryList(){
        log.info("获取岗位类型");
        try {
            QueryWrapper<ResumeCategory> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1);
            List<ResumeCategory> categoryList = resumeCategoryService.list(queryWrapper);
            return BaseResponse.ok(categoryList);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.error(500, "岗位获取失败");
        }
    }

    @GetMapping("/status/{interviewId}")
    @ApiOperation("完成面试，更改状态")
    public BaseResponse<Boolean> setInterviewStatus(
        @PathVariable String interviewId,
        @RequestHeader Long userId) {
        log.info("面试完成，状态更改中，调用AI进行面试评分和面试反馈, userId={}", userId);
        try {
            Interview interview = interviewService.getById(interviewId);
            // 更新面试状态
            interview.setStatus(1);
            Boolean update = interviewService.updateById(interview);
            
            // 如果有用户ID，则更新对应的待办任务状态
            if (userId != null) {
                updateTodoStatus(interviewId, userId);
            }
            
            return BaseResponse.ok(update);
        } catch (Exception e) {
            log.error("更新面试状态失败", e);
            return BaseResponse.error(500, "更新面试状态失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新待办任务状态为已完成
     * 
     * @param interviewId 面试ID
     * @param userId 用户ID
     */
    private void updateTodoStatus(String interviewId, Long userId) {
        try {
            // 获取面试信息，确定面试类型
            Interview interview = interviewService.getById(interviewId);
            if (interview == null) {
                log.warn("更新待办任务状态失败：面试记录不存在, interviewId={}", interviewId);
                return;
            }
            
            // 根据面试类型确定待办任务类型
            Integer todoType = null;
            if ("hr".equals(interview.getStage())) {
                todoType = 2; // hr面试
            } else if ("tech".equals(interview.getStage())) {
                todoType = 3; // 技术面试
            } else if ("formal".equals(interview.getType())) {
                todoType = 4; // 正式面试
            }
            
            if (todoType == null) {
                log.warn("无法确定待办任务类型, interviewId={}, stage={}, type={}", 
                         interviewId, interview.getStage(), interview.getType());
                return;
            }
            // 先尝试获取resumeId
            Long resumeId = null;
            try {
                // 根据userId和面试岗位获取resumeId
                LambdaQueryWrapper<ResumeCategoryRelation> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ResumeCategoryRelation::getUserId, userId)
                       .eq(ResumeCategoryRelation::getCatId, Long.valueOf(interview.getPosition()));
                
                ResumeCategoryRelation relation = resumeCategoryRelationService.getOne(wrapper);
                if (relation != null) {
                    resumeId = relation.getResumeId();
                    log.info("找到对应的resumeId={}, userId={}, position={}", resumeId, userId, interview.getPosition());
                }
            } catch (Exception e) {
                log.warn("获取resumeId失败，将使用userId查询待办任务: {}", e.getMessage());
            }

            // 如果有resumeId，先尝试根据resumeId查询待办任务
            if (resumeId != null) {
                List<SysTodo> todoListByResumeId = sysTodoMapper.selectUnfinishedByResumeId(resumeId);
                for (SysTodo todo : todoListByResumeId) {
                    if (todo.getTodoType().equals(todoType)) {
                        // 更新待办任务状态为已完成
                        todo.setStatus(1); // 1-已完成
                        todo.setUpdateTime(new Date());
                        sysTodoMapper.updateByPrimaryKeySelective(todo);
                        log.info("根据resumeId更新待办任务完成状态: id={}, type={}, resumeId={}", todo.getId(), todoType, resumeId);
                        break; // 只更新一条匹配的记录
                    }
                }
            }
        } catch (Exception e) {
            log.error("更新待办任务状态异常", e);
        }
    }

    @PostMapping("/upload")
    @ApiOperation("上传简历文件")
    public BaseResponse<String> uploadResume(@RequestParam("file") MultipartFile file) {
        log.info("上传简历文件");
        try {
            String resumeText = resumeService.extractResumeText(file);
            return BaseResponse.ok(resumeText);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.error(500, "简历解析失败: " + e.getMessage());
        }
    }

    @PostMapping("/generate-questions")
    @ApiOperation("根据简历内容生成面试问题")
    public BaseResponse<List<String>> generateQuestions(@RequestBody String resumeText) {
        log.info("根据简历内容生成面试问题");
        try {
            List<String> questions = resumeService.generateInterviewQuestions(resumeText);
            log.info("面试问题: {}", questions);
            return BaseResponse.ok(questions);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.error(500, "生成面试问题失败: " + e.getMessage());
        }
    }


    @PostMapping("/analyze")
    @ApiOperation("上传并分析简历，直接返回面试问题")
    public BaseResponse<List<String>> analyzeResume(
            @RequestParam("file") MultipartFile file,
            @ApiParam(value = "面试环节", required = true)
            @RequestParam(value = "interviewStage", required = true) String interviewStage,
            @ApiParam(value = "面试岗位", required = true)
            @RequestParam(value = "interviewPosition", required = true) String interviewPosition,
            @ApiParam(value = "工作经验", required = true)
            @RequestParam(value = "interviewExperience", required = true) String interviewExperience) {
        
        log.info("上传并分析简历，直接返回面试问题");
        log.info("面试环节: {}, 面试岗位: {}, 工作经验: {}", interviewStage, interviewPosition, interviewExperience);
        
        try {
            // 1. 提取简历文本
            String resumeText = resumeService.extractResumeText(file);
            log.info("简历文本: {}", resumeText);
            
            // 2. 生成面试问题
            List<String> questions = resumeService.generateInterviewQuestions(resumeText, interviewStage, interviewPosition, interviewExperience);
            log.info("面试问题: {}", questions);
            
            return BaseResponse.ok(questions);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.error(500, "简历分析失败: " + e.getMessage());
        }
    }
} 