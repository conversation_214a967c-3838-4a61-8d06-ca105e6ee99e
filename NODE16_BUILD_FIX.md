# Node.js 16 构建问题解决方案

## 问题描述
在 Node.js 16 环境下构建前端项目时出现以下错误：
```
The CJS build of Vite's Node API is deprecated
TypeError: crypto$2.getRandomValues is not a function
```

## 根本原因
- Vite 6.x 版本需要 Node.js 18+ 支持
- Node.js 16 缺少某些新的 crypto API
- TypeScript 配置不兼容旧版本 Node.js

## 解决方案

### 方案1：使用修复脚本（推荐）

#### Linux/macOS:
```bash
cd mozai-interview-front
chmod +x fix-node16-build.sh
./fix-node16-build.sh
```

#### Windows:
```cmd
cd mozai-interview-front
fix-node16-build.bat
```

### 方案2：手动修复步骤

1. **清理现有依赖**
```bash
rm -rf node_modules package-lock.json
rm -rf dist-interview-ai .vite
```

2. **重新安装依赖**
```bash
npm install
```

3. **使用兼容性构建**
```bash
npm run build:node16
```

### 方案3：使用新的构建脚本

```bash
# 修复依赖
npm run fix:deps

# Node.js 16 兼容构建
npm run build:node16
```

## 已修改的文件

### 1. package.json
- 降级 Vite 从 6.3.5 到 4.5.5
- 降级 @vitejs/plugin-vue 从 5.2.4 到 4.6.2
- 降级 @types/node 从 20.6.0 到 16.18.0
- 添加新的构建脚本

### 2. tsconfig.json
- 修改 moduleResolution 从 "bundler" 到 "node"

### 3. vite.config.ts
- 添加 global: 'globalThis' 定义
- 保持其他配置不变

## 版本兼容性

| Node.js 版本 | Vite 版本 | 状态 |
|-------------|----------|------|
| 16.x        | 4.5.5    | ✅ 兼容 |
| 18.x        | 6.3.5    | ✅ 兼容 |
| 20.x        | 6.3.5    | ✅ 兼容 |

## 验证构建成功

构建成功后应该看到：
```
✅ 构建完成！
dist-interview-ai/ 目录已生成
```

## 如果仍然失败

1. **检查 Node.js 版本**
```bash
node --version
```

2. **完全清理重装**
```bash
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

3. **使用 yarn 替代 npm**
```bash
yarn install
yarn build
```

4. **升级 Node.js（最佳方案）**
建议升级到 Node.js 18+ 以获得最佳兼容性。

## 注意事项

- 修改后的配置专门针对 Node.js 16 优化
- 如果升级 Node.js 版本，可以恢复使用更新的 Vite 版本
- 构建产物功能不受影响，只是构建工具版本降级
