package com.bimowu.interview.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bimowu.interview.dao.InterviewTranscriptionMapper;
import com.bimowu.interview.model.InterviewTranscription;
import com.bimowu.interview.service.InterviewTranscriptionService;
import com.bimowu.interview.service.TranscriptionService;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 面试转写记录服务实现类
 */
@Slf4j
@Service
@ToString(exclude = {"interviewTranscriptionMapper", "transcriptionService"})
public class InterviewTranscriptionServiceImpl extends ServiceImpl<InterviewTranscriptionMapper, InterviewTranscription>
        implements InterviewTranscriptionService {

    @Autowired
    private InterviewTranscriptionMapper interviewTranscriptionMapper;
    
    @Autowired
    private TranscriptionService transcriptionService;

    @Override
    @Transactional
    public String saveTranscription(MultipartFile audioFile, Integer questionIndex, String interviewId, String question) {
        log.info("保存面试问题的转写结果: interviewId={}, questionIndex={}", interviewId, questionIndex);
        
        // 检查是否已存在该问题的转写记录
        //InterviewTranscription existingRecord = interviewTranscriptionMapper.selectByInterviewIdAndQuestionIndex(interviewId, questionIndex);
        LambdaQueryWrapper<InterviewTranscription> wrapper = new LambdaQueryWrapper<InterviewTranscription>()
                .eq(InterviewTranscription::getInterviewId, interviewId)
                .eq(InterviewTranscription::getQuestionIndex, questionIndex);
        InterviewTranscription existingRecord = interviewTranscriptionMapper.selectOne(wrapper);

                // 调用语音识别服务转写音频
        String transcriptionText = transcriptionService.speechToText(audioFile);
        
        // 获取音频文件的URL（从TranscriptionService中获取）
        String audioUrl = "";
        if (transcriptionService instanceof TranscriptionServiceImpl) {
            audioUrl = ((TranscriptionServiceImpl) transcriptionService).getLastUploadedFileUrl();
            log.info("获取到音频文件URL: {}", audioUrl);
        }
        
        if (existingRecord != null) {
            // 更新已有记录
            existingRecord.setTranscription(transcriptionText);
            existingRecord.setAudioUrl(audioUrl);
            existingRecord.setUpdateTime(new Date());
            interviewTranscriptionMapper.update(existingRecord);
            return transcriptionText;
        } else {
            // 创建新记录
            InterviewTranscription transcription = new InterviewTranscription();
            transcription.setInterviewId(interviewId);
            transcription.setQuestionIndex(questionIndex);
            transcription.setQuestion(question);
            transcription.setTranscription(transcriptionText);
            transcription.setAudioUrl(audioUrl);
            transcription.setCreateTime(new Date());
            transcription.setUpdateTime(new Date());
            interviewTranscriptionMapper.insert(transcription);
            return transcriptionText;
        }
    }
    
    @Override
    @Transactional
    public boolean batchSaveTranscriptions(List<InterviewTranscription> transcriptions) {
        log.info("批量保存转写记录，数量: {}", transcriptions.size());
        
        if (transcriptions == null || transcriptions.isEmpty()) {
            log.warn("转写记录列表为空，没有数据需要保存");
            return false;
        }
        
        try {
            // 设置时间，如果没有的话
            Date now = new Date();
            for (InterviewTranscription transcription : transcriptions) {
                if (transcription.getCreateTime() == null) {
                    transcription.setCreateTime(now);
                }
                if (transcription.getUpdateTime() == null) {
                    transcription.setUpdateTime(now);
                }
            }
            
            // 使用MyBatis-Plus的批量插入方法
            boolean result = this.saveBatch(transcriptions);
            log.info("成功批量保存{}条转写记录", transcriptions.size());
            return result;
        } catch (Exception e) {
            log.error("批量保存转写记录失败", e);
            throw new RuntimeException("批量保存转写记录失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<Integer, String> getInterviewTranscriptions(String interviewId) {
        log.info("获取面试的所有转写结果: interviewId={}", interviewId);
        List<InterviewTranscription> transcriptions = interviewTranscriptionMapper.selectByInterviewId(interviewId);
        
        return transcriptions.stream()
                .collect(Collectors.toMap(
                        InterviewTranscription::getQuestionIndex,
                        InterviewTranscription::getTranscription,
                        (existing, replacement) -> replacement)); // 如果有重复，使用后者
    }

    @Override
    public List<InterviewTranscription> getTranscriptionList(String interviewId) {
        log.info("获取面试的所有转写记录: interviewId={}", interviewId);
        return interviewTranscriptionMapper.selectByInterviewId(interviewId);
    }

    @Override
    public InterviewTranscription getTranscription(String interviewId, Integer questionIndex) {
        log.info("获取面试问题的转写记录: interviewId={}, questionIndex={}", interviewId, questionIndex);
        return interviewTranscriptionMapper.selectByInterviewIdAndQuestionIndex(interviewId, questionIndex);
    }

    @Override
    @Transactional
    public boolean updateTranscription(InterviewTranscription transcription) {
        log.info("更新转写记录: id={}", transcription.getId());
        transcription.setUpdateTime(new Date());
        int result = interviewTranscriptionMapper.update(transcription);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteTranscription(Long id) {
        log.info("删除转写记录: id={}", id);
        int result = interviewTranscriptionMapper.deleteById(id);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteTranscriptionsByInterviewId(String interviewId) {
        log.info("删除面试的所有转写记录: interviewId={}", interviewId);
        int result = interviewTranscriptionMapper.deleteByInterviewId(interviewId);
        return result > 0;
    }
} 